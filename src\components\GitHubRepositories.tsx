import React, { useEffect, useState, useMemo, useCallback } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import SwiperCore from "swiper/core";

// Configure Swiper
SwiperCore.use([Autoplay]);

interface Repository {
  id: number;
  name: string;
  description: string;
  private: boolean;
  html_url: string;
  language: string;
  stargazers_count: number;
  fork: boolean;
}

interface GitHubRepositoriesProps {
  token: string; // GitHub access token
  username: string; // GitHub username
}

interface RepositoryRowProps {
  repositories: Repository[];
  direction: "ltr" | "rtl";
  rowIndex: number;
}

// Individual Row Component
const RepositoryRow: React.FC<RepositoryRowProps> = React.memo(
  ({ repositories, direction, rowIndex }) => {
    // Generate language color - memoized to prevent recreation on each render
    const getLanguageColor = useCallback((language: string | null) => {
      if (!language) return "#777777";

      const colors: { [key: string]: string } = {
        JavaScript: "#FFFF00",
        TypeScript: "#0000FF",
        Python: "#FF0000",
        Java: "#b07219",
        "C#": "#00FFFF",
        PHP: "#4F5D95",
        HTML: "#00FF00",
        CSS: "#563d7c",
        Go: "#00ADD8",
        Ruby: "#701516",
        Swift: "#ffac45",
        Kotlin: "#A97BFF",
        Rust: "#dea584",
        Dart: "#00B4AB",
      };

      return colors[language] || "#10B981"; // Default to theme green
    }, []);

    // Slider settings based on direction - memoized to prevent recreation on each render
    const sliderSettings = useMemo(
      () => ({
        speed: 12000, // Increased for smoother movement
        autoplay: {
          delay: 0,
          disableOnInteraction: false,
        },
        loop: true,
        loopAdditionalSlides: 10, // Add more slides to ensure smooth looping
        slidesPerView: "auto" as "auto",
        allowTouchMove: false,
        spaceBetween: 30,
        slideToClickedSlide: false,
        cssMode: false,
        freeMode: {
          enabled: true,
          momentum: true,
        },
        // Fixed property for direction
        reverseDirection: direction === "rtl",
      }),
      [direction]
    );

    // Memoize the repository cards to prevent unnecessary re-renders
    const renderRepositoryCards = useCallback(
      (repo: Repository, isDuplicate: boolean = false) => (
        <SwiperSlide
          key={`row-${rowIndex}-${isDuplicate ? "dup-" : ""}${repo.id}`}
          className="repository-slide"
        >
          <a
            href={repo.html_url}
            target="_blank"
            rel="noopener noreferrer"
            className="repository-card"
          >
            <div className="repository-header">
              <h3 className="repository-name">{repo.name}</h3>
              <span
                className={`repository-visibility ${
                  repo.private ? "private" : "public"
                }`}
              >
                {repo.private ? "Private" : "Public"}
              </span>
            </div>

            <div className="repository-description">
              {repo.description || "No description provided"}
            </div>

            <div className="repository-footer">
              {repo.language && (
                <div className="repository-language">
                  <span
                    className="language-color"
                    style={{
                      backgroundColor: getLanguageColor(repo.language),
                    }}
                  ></span>
                  <span>{repo.language}</span>
                </div>
              )}

              <div className="repository-stars">
                <span className="star-icon">★</span>
                <span>{repo.stargazers_count}</span>
              </div>
            </div>
          </a>
        </SwiperSlide>
      ),
      [rowIndex, getLanguageColor]
    );

    // Memoize all repository slides to prevent re-rendering when parent re-renders
    const repositorySlides = useMemo(() => {
      return (
        <>
          {repositories.map((repo) => renderRepositoryCards(repo))}
          {/* Add duplicate slides to ensure continuous scrolling */}
          {repositories.map((repo) => renderRepositoryCards(repo, true))}
        </>
      );
    }, [repositories, renderRepositoryCards]);

    return (
      <div className="github-repositories-row">
        <Swiper
          {...sliderSettings}
          className="repositories-swiper"
          modules={[Autoplay]}
        >
          {repositorySlides}
        </Swiper>
      </div>
    );
  }
);

// Main Component
const GitHubRepositories: React.FC<GitHubRepositoriesProps> = ({
  token,
  username,
}) => {
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Memoize fetch function to prevent recreation on re-renders
  const fetchAllRepositories = useCallback(async () => {
    try {
      setLoading(true);
      let allRepos: Repository[] = [];
      let page = 1;
      let hasMorePages = true;

      // Use a different API endpoint to fetch both public and private repos
      // This endpoint returns the authenticated user's repositories (both public and private)
      const baseUrl = `https://api.github.com/user/repos`;

      // Fetch all repositories with pagination
      while (hasMorePages) {
        const response = await fetch(
          `${baseUrl}?per_page=100&page=${page}&affiliation=owner`,
          {
            headers: {
              Authorization: `token ${token}`,
              Accept: "application/vnd.github.v3+json",
            },
          }
        );

        if (!response.ok) {
          throw new Error(`Error fetching repositories: ${response.status}`);
        }

        const data = await response.json();

        // Add this page's repositories to our collection
        allRepos = [...allRepos, ...data];

        // Check if we've reached the end of the pages
        if (data.length < 100) {
          hasMorePages = false;
        } else {
          page++;
        }
      }

      // Sort all repositories by name
      const sortedRepos = allRepos.sort((a: Repository, b: Repository) =>
        a.name.localeCompare(b.name)
      );

      console.log(
        `Fetched a total of ${allRepos.length} repositories (including private)`
      );
      setRepositories(sortedRepos);
      setLoading(false);
    } catch (err) {
      console.error("Error fetching repositories:", err);
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
      setLoading(false);
    }
  }, [token]);

  useEffect(() => {
    if (token && username) {
      fetchAllRepositories();
    }
  }, [token, username, fetchAllRepositories]);

  // Helper function to split repositories into rows - memoized
  const splitIntoRows = useCallback((repos: Repository[]): Repository[][] => {
    if (repos.length === 0) return [[], [], [], [], []];

    const reposPerRow = Math.ceil(repos.length / 5);
    return [
      repos.slice(0, reposPerRow),
      repos.slice(reposPerRow, reposPerRow * 2),
      repos.slice(reposPerRow * 2, reposPerRow * 3),
      repos.slice(reposPerRow * 3, reposPerRow * 4),
      repos.slice(reposPerRow * 4),
    ];
  }, []);

  // Memoize the split repositories to prevent recalculation on each render
  const repoRows = useMemo(
    () => splitIntoRows(repositories),
    [repositories, splitIntoRows]
  );

  if (loading) {
    return (
      <div className="github-repositories-loading">
        <div className="loading-spinner"></div>
        <p>Loading repositories...</p>
      </div>
    );
  }

  if (error) {
    return <div className="github-repositories-error">Error: {error}</div>;
  }

  return (
    <section className="github-repositories-section">
      <div className="section-title section-title-3 text-center mb-60">
        <h2 className="title">Projects</h2>
      </div>

      <div className="github-repositories-container">
        {/* Row 1: Left to Right */}
        <RepositoryRow
          repositories={repoRows[0]}
          direction="ltr"
          rowIndex={0}
        />

        {/* Row 2: Right to Left */}
        <RepositoryRow
          repositories={repoRows[1]}
          direction="rtl"
          rowIndex={1}
        />

        {/* Row 3: Left to Right */}
        <RepositoryRow
          repositories={repoRows[2]}
          direction="ltr"
          rowIndex={2}
        />

        {/* Row 4: Right to Left */}
        <RepositoryRow
          repositories={repoRows[3]}
          direction="rtl"
          rowIndex={3}
        />

        {/* Row 5: Left to Right */}
        <RepositoryRow
          repositories={repoRows[4]}
          direction="ltr"
          rowIndex={4}
        />
      </div>
    </section>
  );
};

export default GitHubRepositories;
