import { useState } from "react";
import {
  <PERSON><PERSON>arallaxContainer,
  Mouse<PERSON>arallaxChild,
} from "react-parallax-mouse";
import { NavLink } from "react-router-dom";
// import { scroller } from "react-scroll";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const slider_setting = {
  dots: false,
  infinite: true,
  speed: 8000,
  autoplay: true,
  autoplaySpeed: 0,
  cssEase: "linear",
  slidesToShow: 7,
  slidesToScroll: 1,
  variableWidth: true,
  arrows: false,
  responsive: [
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 5,
        slidesToScroll: 1,
        infinite: true,
        arrows: false,
      },
    },
    {
      breakpoint: 992,
      settings: {
        slidesToShow: 4,
        slidesToScroll: 1,
        arrows: false,
      },
    },
    {
      breakpoint: 767,
      settings: {
        slidesToShow: 4,
        slidesToScroll: 1,
        arrows: false,
      },
    },
    {
      breakpoint: 575,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 1,
        arrows: false,
      },
    },
  ],
};

// brands
const brands: string[] = [
  "/assets/img/brand/brand_logo05.png",
  "/assets/img/brand/brand_logo07.png",
  "/assets/img/brand/brand_logo06.png",
  "/assets/img/brand/brand_logo01.png",
  "/assets/img/brand/brand_logo02.png",
  "/assets/img/brand/brand_logo03.png",
  "/assets/img/brand/brand_logo04.png",
  "/assets/img/brand/brand_logo08.png",
  "/assets/img/brand/brand_logo09.png",
  "/assets/img/brand/brand_logo10.png",
  "/assets/img/brand/8.png",
  "/assets/img/brand/9.png",
  "/assets/img/brand/10.png",
  "/assets/img/brand/s.png",
  "/assets/img/brand/11.png",
  "/assets/img/brand/12.png",
  "/assets/img/brand/13.png",
  "/assets/img/brand/14.png",
  "/assets/img/brand/15.png",
  "/assets/img/brand/16.png",
  "/assets/img/brand/17.png",
  "/assets/img/brand/18.png",
  "/assets/img/brand/19.png",
  "/assets/img/brand/20.png",
  "/assets/img/brand/21.png",
  "/assets/img/brand/22.png",
  "/assets/img/brand/23.png",
  "/assets/img/brand/24.png",
  "/assets/img/brand/25.png",
];
const HeroBanner = () => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  // const scrollToContact = () => {
  //   scroller.scrollTo("contact", {
  //     duration: 800,
  //     delay: 0,
  //     smooth: "easeInOutQuart",
  //   });
  // };
  return (
    <MouseParallaxContainer>
      <section
        className="slider__area slider__bg moboh"
        style={{ marginTop: "-8vh" }}
        // style={{ backgroundImage: `url(/assets/img/slider/slider_bg.jpg)` }}
      >
        <div className="slider-activee">
          <div className="single-slider">
            <div className="container custom-container">
              <div className="row justify-content-between">
                <div className="col-lg-6">
                  <div className="slider__content mobo">
                    <h6 className="sub-title wow fadeInUp" data-wow-delay=".2s">
                      Developer
                    </h6>
                    <h2 className="title wow fadeInUp" data-wow-delay=".5s">
                      Blesslin.
                    </h2>
                    <p className="wow fadeInUp lowerz" data-wow-delay=".8s">
                      who writes the code ? posterity_
                    </p>
                    <div style={{ gap: 25 }} className="mblock">
                      <NavLink
                        to="https://drive.google.com/file/d/11PAGBhObXop_CE4lXIpLyt_a33paTmmi/view?usp=sharing"
                        className="w4"
                        target="_blank"
                      >
                        <div
                          className="slider__btn wow fadeInUp sbtn hirey"
                          style={{ backgroundColor: "#000" }}
                          data-wow-delay="1.2s"
                        >
                          <span style={{ color: "#FFF" }}>
                            <b>Updated Resume / CV </b>
                            <i className="fas fa-bolt"></i>
                          </span>
                        </div>
                      </NavLink>
                      {/* <NavLink
                        to="https://www.instagram.com/blessl.in"
                        className="w4"
                      >
                        <div
                          className="slider__btn wow fadeInUp sbtn hirey"
                          style={{ backgroundColor: "#000" }}
                          data-wow-delay="1.2s"
                        >
                          <span style={{ color: "#fff" }}>
                            <b>Gallery </b>
                            <i className="fas fa-image"></i>
                          </span>
                        </div>
                      </NavLink> */}
                      {/* <NavLink
                        to="https://drive.google.com/drive/folders/1xu8FTYGOkCFTXW7IrXBXGZyjuB2HjUzL?usp=sharing"
                        className="w4"
                        target="_blank"
                      >
                        <div
                          className="slider__btn wow fadeInUp sbtn hirey"
                          style={{ backgroundColor: "#000" }}
                          data-wow-delay="1.2s"
                        >
                          <span style={{ color: "#fff" }}>
                            <b>Showcase </b>
                            <i className="fas fa-images"></i>
                          </span>
                        </div>
                      </NavLink> */}
                      {/* <NavLink
                        to="https://docs.google.com/spreadsheets/d/1qWkcRLnMqgr-Ij5nejytdELz0laeeWsC6A9Em8tnbLw/edit?usp=drivesdk"
                        className="w4"
                        target="_blank"
                      >
                        <div
                          className="slider__btn wow fadeInUp sbtn hirey"
                          style={{ backgroundColor: "#000" }}
                          data-wow-delay="1.2s"
                        >
                          <span style={{ color: "#fff" }}>
                            <b>Clients </b>
                            <i className="fas fa-briefcase"></i>
                          </span>
                        </div>
                      </NavLink> */}
                    </div>
                    {/* <NavLink to="#" onClick={scrollToContact}>
                      <div
                        className="slider__btn wow fadeInUp sbtn hirey"
                        style={{ backgroundColor: "#000" }}
                        data-wow-delay="1.2s"
                      >
                        <span style={{ color: "#fff" }}>
                          <b>Hire - 7 days free trial.</b>
                        </span>
                      </div>
                    </NavLink> */}
                  </div>
                </div>
                <div className="col-xxl-6 col-xl-5 col-lg-6 mobo-up">
                  <MouseParallaxChild
                    factorX={0.03}
                    factorY={0.03}
                    className="slider__img text-center"
                  >
                    <img
                      // src="/assets/img/slider/slider_img01.png"
                      // src="/assets/img/others/ghost.jpg"
                      src="/assets/img/others/blesslin.png"
                      alt="img"
                      className="ghost"
                      style={{
                        width: 400,
                        borderRadius: "12px",
                        marginTop: "13vh",
                        // marginLeft: "2vw",
                      }}
                    />
                  </MouseParallaxChild>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="slider__shapes mobo-logo-up"
          style={{ marginTop: "30vh" }}
        >
          {/* <img src="/assets/img/slider/slider_shape01.png" alt="shape" />
          <img src="/assets/img/slider/slider_shape02.png" alt="shape" />
          <img src="/assets/img/slider/slider_shape03.png" alt="shape" />
          <img src="/assets/img/slider/slider_shape04.png" alt="shap1e" /> */}
        </div>
        <div className="slider__brand-wrap">
          <div className="container custom-container">
            <Slider
              {...slider_setting}
              className="slider__brand-list list-wrap"
            >
              {brands.map((b, i) => (
                <div key={i} style={{ padding: 50 }}>
                  <li
                    onMouseEnter={() => setHoveredIndex(i)}
                    onMouseLeave={() => setHoveredIndex(null)}
                    style={{
                      opacity:
                        hoveredIndex === null || hoveredIndex === i ? 1 : 0.3,
                      transition: "opacity 0.3s",
                    }}
                  >
                    <NavLink to="#">
                      <img src={b} alt="brand" style={{ width: 100 }} />
                    </NavLink>
                  </li>
                </div>
              ))}
            </Slider>
            {/* <ul
              className="slider__brand-list list-wrap w-full"
              style={{ marginTop: 20 }}
            >
              {brands.slice(5, 10).map((b, i) => (
                <li
                  onMouseEnter={() => setHoveredIndex(i)}
                  onMouseLeave={() => setHoveredIndex(null)}
                  style={{
                    opacity:
                      hoveredIndex === null || hoveredIndex === i ? 1 : 0.3,
                    transition: "opacity 0.3s",
                  }}
                  key={i}
                >
                  <NavLink to="#">
                    <img src={b} alt="brand" style={{ width: 100 }} />
                  </NavLink>
                </li>
              ))}
            </ul> */}
          </div>
        </div>
      </section>
    </MouseParallaxContainer>
  );
};

export default HeroBanner;
