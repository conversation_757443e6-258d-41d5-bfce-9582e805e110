import { NavLink } from "react-router-dom";
import CounterUp from "../common/counter-up";
import TextAnimation from "../common/text-animation";
import SvgIconCom from "../common/svg-icon-anim";
import { useState } from "react";
import VideoPopup from "../common/video-popup";

// nav button
type INavProps = { id: string; img: string; isActive?: boolean };
function NavBtn({ id, img, isActive }: INavProps) {
  // handle open search
  const handleClickSound = (audioPath: string) => {
    const audio = new Audio(audioPath);
    audio.play();
  };
  return (
    <li className="nav-item" role="presentation">
      <button
        className={`nav-link ${isActive ? "active" : ""}`}
        id={`about${id}-tab`}
        data-bs-toggle="tab"
        data-bs-target={`#about${id}`}
        type="button"
        role="tab"
        aria-controls={`about${id}`}
        aria-selected={isActive ? "true" : "false"}
        tabIndex={-1}
        onClick={() => handleClickSound("/assets/audio/tab.mp3")}
        style={{ border: "none" }}
      >
        <span className="img-shape"></span>
        <img src={img} alt="img" style={{ borderRadius: 0 }} />
      </button>
    </li>
  );
}

// tab content item
// prop type
// type ITabProps = {
//   id: string;
//   img: string;
//   title: string;
//   rate: string;
//   isActive?: boolean;
// };
// function TabItem({ id, img, title, rate, isActive }: ITabProps) {
//   return (
//     <div
//       className={`tab-pane ${isActive ? "show active" : ""}`}
//       id={`about${id}`}
//       role="tabpanel"
//       aria-labelledby={`about${id}-tab`}
//     >
//       <div className="row justify-content-center">
//         <div className="col-xl-5 col-lg-10">
//           <div className="about__img">
//             <img src={img} alt="img" />
//           </div>
//         </div>
//         <div className="col-xl-7 col-lg-10">
//           <div className="about__flex-wrap">
//             <div className="about__content-wrap">
//               <div className="about__content">
//                 <h4 className="title">{title}</h4>
//                 <span className="rate">rate {rate}</span>
//                 <p>
//                   Lorem ipsum dolor sit amet, consteur adipiscing Duis elementum
//                   sollicitudin is yaugue euismods Nulla ullamcorper. Morbi
//                   pharetra tellus miolslis, tincidunt massa venenatis.
//                 </p>
//               </div>
//               <div className="about__content-list">
//                 <ul className="list-wrap">
//                   <li>
//                     <img
//                       src="/assets/img/icons/features_icon01.png"
//                       alt="img"
//                     />{" "}
//                     Chichi Dragon Ball
//                   </li>
//                   <li>
//                     <img
//                       src="/assets/img/icons/features_icon02.png"
//                       alt="img"
//                     />{" "}
//                     Space Babe Night
//                   </li>
//                   <li>
//                     <img
//                       src="/assets/img/icons/features_icon03.png"
//                       alt="img"
//                     />{" "}
//                     Dragon Ball
//                   </li>
//                 </ul>
//               </div>
//             </div>
//             <div className="about__btn-wrap">
//               <ul className="list-wrap">
//                 <li>
//                   <NavLink to="/shop-details">Dragon Ball</NavLink>
//                 </li>
//                 <li>
//                   <NavLink to="/shop">nft market</NavLink>
//                 </li>
//                 <li>
//                   <NavLink to="/contact">support</NavLink>
//                 </li>
//               </ul>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }

const AboutArea = () => {
  const [isVideoOpen, setIsVideoOpen] = useState<boolean>(false);
  const today = new Date();
  const date = `${String(today.getDate()).padStart(2, "0")}/${String(
    today.getMonth() + 1
  ).padStart(2, "0")}/${today.getFullYear()}`;
  return (
    <>
      <section
        className="about__area-two section-pt-160"
        style={{ paddingBottom: 100 }}
      >
        <div className="container">
          <div className="row justify-content-center align-items-center">
            <div className="col-xl-6 col-lg-6 order-0 order-lg-2">
              {/* <div className="about__funFact-images"> */}
              <img
                src="/assets/img/com/jzoho.png"
                alt="background"
                className="bg-shape"
              />
              {/* <img
                  src="/assets/img/com/jzoho.png"
                  className="main-img"
                  alt="image"
                /> */}
              {/* </div> */}
              <div className="about__funFact-trophy">
                <div className="icon">
                  <img src="/assets/img/icons/trophy.png" alt="trophy" />
                </div>
                <div className="content">
                  <h5 className="sign">Blesslin Jerish</h5>
                  <p className="signx" style={{ marginTop: 16, fontSize: 10 }}>
                    {date}
                  </p>
                </div>
              </div>
            </div>
            <div className="col-xl-6 col-lg-6 col-md-10">
              <div className="section__title text-start mb-30">
                <h3 className="title">
                  The Journey of <br /> <span style={{textTransform: "lowercase"}}>Blessl.in</span>
                </h3>
              </div>
              <div className="about__content-two">
                <p>
                  Coding for the past 6 years, my cutting edge skills have
                  become an all rounded trusted player in the global IT
                  industry. Completing an impressive portfolio of 225+
                  successful projects with an base of 32 satisfied clients. I
                  pride myself on my innovative solutions and timely project
                  deliveries.
                  <br />
                  <br />
                  Happy Days,
                  <br />
                  Blesslin
                </p>
              </div>
              <div className="about__content-bottom">
                <div className="about__funFact-wrap">
                  <div
                    className="about__funFact-lists"
                    style={{ width: "100%" }}
                  >
                    <div className="about__funFact-item">
                      <h2 className="count">
                        <CounterUp number={225} text="+" />
                      </h2>
                      <p>Projects</p>
                    </div>
                    <div className="about__funFact-item">
                      <h2 className="count">
                        <CounterUp number={32} text="" />
                      </h2>
                      <p>Clients</p>
                    </div>
                    <div className="about__funFact-item">
                      <h2 className="count">
                        <CounterUp number={6} text="+" />
                      </h2>
                      <p>Experience</p>
                    </div>
                    {/* <div className="about__funFact-item">
                      <h2 className="count">
                        <CounterUp number={23} text="+" />
                      </h2>
                      <p>Offers</p>
                    </div> */}
                  </div>
                  <div className="about__content-btns" style={{ padding: 5 }}>
                    <NavLink to="/contact" className="tg-btn-3 tg-svg">
                      <SvgIconCom
                        icon="/assets/img/icons/shape.svg"
                        id="svg-6"
                      />
                      <span>Hire me ? </span>
                    </NavLink>
                    {/* <a
                      className="popup-video cursor-pointer"
                      onClick={() => setIsVideoOpen(true)}
                    >
                      <i className="fas fa-play"></i>
                      <span className="text">How we work ?</span>
                    </a> */}
                  </div>
                </div>
                <div className="about__content-circle rounder">
                  <img src="/assets/img/icons/circle.svg" alt="img" />
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 150 150"
                    version="1.1"
                  >
                    <path id="textPath" d="M 0,75 a 75,75 0 1,1 0,1 z"></path>
                    <text>
                      <textPath href="#textPath">Blessl.in</textPath>
                    </text>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="about__area section-pt-130">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-xl-6 col-lg-7 col-md-10">
              <div className="section__title text-center mb-60">
                <TextAnimation title="My projects" />
                <h3 className="title">top rated skills</h3>
              </div>
            </div>
          </div>
          <div className="row justify-content-center">
            <div className="col-xl-10">
              <div className="about__tab-wrap">
                {/* <div className="about__buttons">
                <NavLink to="/shop" className="tg-btn-2">
                  buy Hero
                </NavLink>
                <NavLink to="/shop" className="tg-btn-2 -secondary">
                  buy nfts
                </NavLink>
              </div> */}
                <ul className="nav nav-tabs" id="myTab" role="tablist">
                  <NavBtn
                    id="05"
                    img={"/assets/img/brand/brand_logo05.png"}
                    isActive={true}
                  />
                  <NavBtn id="07" img={"/assets/img/brand/brand_logo07.png"} />
                  <NavBtn id="06" img={"/assets/img/brand/brand_logo06.png"} />
                  <NavBtn id="03" img={"/assets/img/brand/brand_logo01.png"} />
                  <NavBtn id="04" img={"/assets/img/brand/brand_logo02.png"} />
                  <NavBtn id="05" img={"/assets/img/brand/brand_logo03.png"} />
                  <NavBtn id="06" img={"/assets/img/brand/brand_logo04.png"} />
                  <NavBtn id="10" img={"/assets/img/brand/brand_logo10.png"} />
                </ul>
              </div>
            </div>
          </div>
          {/* <div className="tab-content" id="myTabContent">
          <TabItem
            id="01"
            img={"/assets/img/others/about_img01.jpg"}
            isActive={true}
            title="human game"
            rate="50%"
          />
          <TabItem
            id="02"
            img={"/assets/img/others/about_img02.jpg"}
            title="Axie Infinity"
            rate="60%"
          />
          <TabItem
            id="03"
            img={"/assets/img/others/about_img03.jpg"}
            title="The Walking Dead"
            rate="75%"
          />
          <TabItem
            id="04"
            img={"/assets/img/others/about_img04.jpg"}
            title="The Dogami"
            rate="65%"
          />
          <TabItem
            id="05"
            img={"/assets/img/others/about_img05.jpg"}
            title="The Sandbox"
            rate="85%"
          />
          <TabItem
            id="06"
            img={"/assets/img/others/about_img06.jpg"}
            title="Pegaxy Horses"
            rate="90%"
          />
        </div> */}
        </div>
      </section>
      <VideoPopup
        isVideoOpen={isVideoOpen}
        setIsVideoOpen={setIsVideoOpen}
        videoId={"ssrNcwxALS4"}
      />
    </>
  );
};

export default AboutArea;
