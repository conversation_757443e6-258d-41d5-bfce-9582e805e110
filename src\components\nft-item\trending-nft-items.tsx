import { Swiper, SwiperSlide } from "swiper/react";
import SwiperCore from "swiper/core";
import { Navigation, Keyboard } from "swiper/modules";
import nft_data from "../../data/nft-data";
import { NavLink } from "react-router-dom";

SwiperCore.use([Navigation, Keyboard]);

// slider setting
const slider_setting = {
  observer: true,
  observeParents: true,
  loop: true,
  slidesPerView: 3,
  spaceBetween: 30,
  breakpoints: {
    "1500": {
      slidesPerView: 3,
    },
    "1200": {
      slidesPerView: 3,
    },
    "992": {
      slidesPerView: 2,
    },
    "768": {
      slidesPerView: 2,
    },
    "576": {
      slidesPerView: 1,
    },
    "0": {
      slidesPerView: 1,
    },
  },
  navigation: {
    nextEl: ".slider-button-next",
    prevEl: ".slider-button-prev",
  },
  keyboard: true,
  onlyInViewport: true,
};

const TrendingNftItems = () => {
  return (
    <section
      className="trendingNft-area section-pt-120"
      style={{ paddingTop: 0 }}
    >
      <div className="container">
        <div className="trendingNft__title-wrap">
          <div className="row">
            <div className="col-md-7">
              <div className="trendingNft__title">
                <h2 className="title">Protagonists_</h2>
              </div>
            </div>
            <div className="col-md-5" style={{ zIndex: 9999999 }}>
              <div className="trendingNft__nav">
                <button className="slider-button-prev">
                  <i className="fas fa-angle-left"></i>
                </button>
                <button className="slider-button-next">
                  <i className="fas fa-angle-right"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        <Swiper
          {...slider_setting}
          modules={[Navigation]}
          className="swiper-container trendingNft-active mAdj"
        >
          {nft_data
            .filter((i) => i.trending)
            .map((item) => (
              <SwiperSlide key={item.id}>
                <div className="trendingNft__item" style={{ height: 480 }}>
                  <div
                    className="trendingNft__item-top pdAdj"
                    style={{ paddingTop: 20 }}
                  >
                    <div className="trendingNft__item-avatar">
                      {/* <div className="image">
                        <NavLink to="/shop-details">
                          <img src={item.creator} alt="img" />
                        </NavLink>
                      </div> */}
                      <div className="info">
                        <h6 className="name">{item.title}</h6>
                        <li
                          className="userName"
                          style={{ textDecoration: "none", fontSize: 16 }}
                        >
                          @{item.creator_name}
                        </li>
                      </div>
                    </div>
                    {/* <div className="trendingNft__item-wish">
                      <NavLink to="#">
                        <i className="far fa-heart"></i>
                      </NavLink>
                    </div> */}
                  </div>
                  <div className="trendingNft__item-image">
                    <NavLink to="/shop-details">
                      <img src={item.img} alt="img" className="keyImg" />
                    </NavLink>
                  </div>
                  <div className="trendingNft__item-bottom">
                    <div className="trendingNft__item-price">
                      {/* <span className="bid">Last Bid</span> */}
                      <h6 className="eth">
                        {/* <i className="fab fa-ethereum"></i>  */}
                        {item.eth}
                        {/* <span>Eth</span> */}
                      </h6>
                    </div>
                    <NavLink to="/shop-details" className="bid-btn">
                      Hire <i className="fas fa-long-arrow-alt-right"></i>
                    </NavLink>
                  </div>
                </div>
              </SwiperSlide>
            ))}
        </Swiper>
      </div>
    </section>
  );
};

export default TrendingNftItems;
