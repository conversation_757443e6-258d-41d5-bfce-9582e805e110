@use "../utils" as *;
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Playwrite+CL:wght@100..400&display=swap");
/*-------------------------------------------

   Theme Name: MYKD - eSports and Gaming NFT Template
   Author : ThemeGenix
   Support: <EMAIL>
   Description: MYKD - eSports and Gaming NFT Template
   Version: 1.0

----------------------------------------------

/************ TABLE OF CONTENTS ***************

    01. Theme Default CSS
    02. Header
    03. Slider
    04. NFT
    05. About
    06. Gallery
    07. Team
    08. Video
    09. RoadMap
    10. Upcoming Match
    11. Match Result
    12. Services
    13. Social Profile
    14. Tournament
    15. Brand
    16. Shop
    17. Blog
    18. Contact
    19. Footer
    20. All Keyframes Here

**********************************************/

/*----------------------------------------*/
/*  01. Theme Default CSS
/*----------------------------------------*/

@import url($font-url);

/*=============================
	Typography css start
===============================*/
body {
  font-family: var(--tg-body-font-family);
  font-size: var(--tg-body-font-size);
  font-weight: var(--tg-fw-regular);
  color: var(--tg-body-color);
  line-height: var(--tg-body-line-height);
  background: var(--tg-common-color-black);
}
img,
.img {
  max-width: 100%;
  @include transition(0.3s);
}

.mblock {
  display: flex;
}

.hirey {
  border: 2px solid #fff;
}

.rounder {
  position: absolute !important;
  margin-left: 32vw !important;
  margin-top: -10vh !important;
}

.w4 {
  flex: 1;
}
.area-background .section-pt-160 {
  padding-top: 0 !important;
}

.slider__brand-list {
  width: 100% !important;
}

.lowerz {
  text-transform: lowercase !important;
}
.slider__brand-wrap .custom-container .slick-slider .slick-list .slick-track {
  display: flex;
  gap: 50px;
}

.sbtn {
  font-family: "Courier New", Courier, monospace;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  background-color: #fff;
  width: 100%;
  height: 7vh;
  border-radius: 7px;
}

.sign {
  text-transform: capitalize;
  font-family: "Playwrite CL", cursive;
  font-optical-sizing: auto;
  font-style: normal;
}

.signx {
  font-family: "Playwrite CL", cursive;
}

.brand__link img {
  max-height: 400px !important;
}

a,
button {
  color: var(--tg-theme-primary);
  outline: none;
  text-decoration: none;
  @include transition(0.3s);
}
a:focus,
.btn:focus,
.button:focus {
  text-decoration: none;
  outline: none;
  @include box-shadow(none);
}
a:hover,
button:hover {
  color: var(--tg-theme-secondary);
  text-decoration: none;
}
button:focus,
input:focus,
input:focus,
textarea,
textarea:focus {
  outline: 0;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--tg-heading-font-family);
  color: var(--tg-heading-color);
  margin-top: 0px;
  font-weight: var(--tg-fw-bold);
  line-height: var(--tg-heading-line-height);
  text-transform: uppercase;
}
h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
  color: inherit;
}
h1 {
  font-size: 2.5rem;
}
h2 {
  font-size: 2rem;
}
h3 {
  font-size: 1.75rem;
}
h4 {
  font-size: 1.5rem;
}
h5 {
  font-size: 1.25rem;
}
h6 {
  font-size: 1rem;
}
.list-wrap {
  margin: 0px;
  padding: 0px;
  & li {
    list-style: none;
  }
}
p {
  font-family: var(--tg-body-font-family);
  font-size: var(--tg-body-font-size);
  line-height: var(--tg-body-line-height);
  font-weight: var(--tg-fw-regular);
  color: var(--tg-body-color);
  margin-bottom: 15px;
}
hr {
  border-bottom: 1px solid var(--tg-common-color-gray);
  border-top: 0 none;
  margin: 30px 0;
  padding: 0;
}
label {
  color: var(--tg-heading-color);
  cursor: pointer;
  font-size: var(--tg-body-font-size);
  font-weight: var(--tg-fw-regular);
}
input,
textarea {
  color: var(--tg-body-color);
}
input[type="color"] {
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  background: none;
  border: 0;
  cursor: pointer;
  height: 100%;
  width: 100%;
  padding: 0;
  border-radius: 50%;
}
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: var(--tg-common-color-black);
}
::-webkit-scrollbar {
  width: 8px;
  background-color: var(--tg-common-color-black);
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: var(--tg-theme-primary);
}
*::-moz-selection {
  background: var(--tg-theme-primary);
  color: var(--tg-common-color-black);
  text-shadow: none;
}
::-moz-selection {
  background: var(--tg-theme-primary);
  color: var(--tg-common-color-black);
  text-shadow: none;
}
::selection {
  background: var(--tg-theme-primary);
  color: var(--tg-common-color-black);
  text-shadow: none;
}

/*=============================
    - Input Placeholder
===============================*/
*::-moz-placeholder {
  color: var(--tg-body-color);
  font-size: var(--tg-body-font-size);
  opacity: 1;
}
*::placeholder {
  color: var(--tg-body-color);
  font-size: var(--tg-body-font-size);
  opacity: 1;
}

/*=============================
    - Common Classes
===============================*/
.fix {
  overflow: hidden;
}
.clear {
  clear: both;
}
.main--area {
  overflow-x: hidden;
}
.cursor-pointer {
  cursor: pointer;
}
/*=============================
    - Bootstrap Custom
=============================*/
.container {
  padding-left: 15px;
  padding-right: 15px;
}
.row {
  --bs-gutter-x: 30px;
}
.gutter-y-30 {
  --bs-gutter-y: 30px;
}
.gx-0 {
  --bs-gutter-x: 0;
}
.container {
  max-width: 1300px;
  @media #{$xl} {
    max-width: 1260px;
  }
  @media #{$lg} {
    max-width: 960px;
  }
  @media #{$md} {
    max-width: 720px;
  }
  @media #{$xs} {
    max-width: 100%;
  }
  @media #{$sm} {
    max-width: 540px;
  }
}
.custom-container {
  max-width: 1590px;
  @media #{$xxl} {
    max-width: 1490px;
  }
  @media #{$xl} {
    max-width: 1260px;
  }
  @media #{$lg} {
    max-width: 960px;
  }
  @media #{$md} {
    max-width: 720px;
  }
  @media #{$xs} {
    max-width: 100%;
  }
  @media #{$sm} {
    max-width: 540px;
  }
}

.include-bg {
  @include background();
}

.nav-tabs img {
  width: 50px;
  background-color: #000000;
}

.gallery__thumb img {
  border: none !important;
}

.gallery-img {
  width: 800px !important;
  margin-left: 3.5rem !important;
}

.title {
  font-family: "Poppins", sans-serif !important;
  font-weight: 900 !important;
}

.title-2 {
  font-weight: 400 !important;
}

.form-control {
  color: rgba(255, 255, 255, 0.7) !important;
}

.nft-item__box {
  height: 22vh;
}

@media (max-width: 600px) {
  // .mob-only {
  //   display: block !important;
  // }
  // .desk-only {
  //   display: none !important;
  // }

  html,
  body {
    overflow-x: hidden !important;
  }
  .mblock {
    display: block !important;
  }

  .moboh {
    padding-top: 200px !important;
  }

  .ghost {
    max-width: 100% !important;
    height: 23vh !important;
    margin-top: 150px !important;
    object-fit: contain !important;
    margin: auto;
  }

  .gb {
    width: 10vh !important;
  }

  .gbb {
    padding-bottom: 30vh !important;
  }

  .rounder {
    position: relative !important;
    margin-left: 32vw !important;
    margin-top: -2vh !important;
    margin-bottom: 3vh !important;
  }

  .nft-item__area,
  .section-pt-160,
  .section-pt-130,
  .section-pt-150 {
    padding-top: 0px !important;
  }

  .tx {
    margin-top: -100px !important;
  }

  .brand-area {
    margin-top: -100px !important;
  }

  .nft-item__area {
    margin-top: -100px !important;
  }

  .mbx {
    padding-bottom: 50px;
  }

  .nft-item__box {
    height: 100%;
  }

  .sbtn {
    width: 80%;
    height: 5vh;
    translate: 15%;
  }
  .contact-area {
    margin-top: -50px !important;
  }

  #contact-form {
    margin-top: 50px !important;
  }

  .logo {
    margin-top: -2rem !important;
  }

  .logo img {
    width: 120px !important;
  }

  .mobo {
    margin-top: 0 !important;
  }

  .title {
    font-size: 40px !important;
  }

  .mobo-up {
    margin-top: -10rem !important;
  }

  .mobo-logo-up {
    margin-top: 3rem !important;
  }

  .brand-active .col {
    margin-left: -4vw !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .pdAdj {
    padding-top: 50px !important;
  }

  .mAdj {
    margin-top: -80px !important;
  }
  .gallery-img {
    margin-left: 0 !important;
  }
  .mlx {
    margin-left: 7vw !important;
  }
}
