import { NavLink } from "react-router-dom";
import ContactForm from "../forms/contact-form";

const ContactArea = () => {
  return (
    <section className="contact-area">
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-lg-6 col-md-10">
            <div className="contact__content">
              <h2 className="overlay-title">
                <span style={{ color: "#00FF00", marginTop: -90 }}>
                  Hire Me
                </span>
              </h2>
              <h2 className="title">
                {/* free of cost - <span style={{ color: "#00FF00" }}>7 days</span> */}
                & Build your dream project.
              </h2>
              <p>
                Embark on a journey to digital excellence! Let me transform your
                ideas into stunning websites and dynamic mobile apps. Reach out
                to me. where innovation meets expertise & your vision becomes
                reality.
              </p>
              <div className="footer-el-widget">
                <h4 className="title">contact me</h4>
                <ul className="list-wrap">
                  <li>
                    {/* <NavLink to="tel:123">+971 333 222 557</NavLink> */}
                    +91 8072981518
                  </li>
                  <li>
                    <NavLink to="mailto:<EMAIL>">
                      <EMAIL>
                    </NavLink>
                  </li>
                  <li>Remote, India</li>
                </ul>
              </div>
            </div>
          </div>
          <div className="col-lg-6 col-md-10">
            <div className="contact__form-wrap">
              {/* form start */}
              <ContactForm />
              {/* form end */}
              <p className="ajax-response"></p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactArea;
