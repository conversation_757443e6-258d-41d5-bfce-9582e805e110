@use '../utils' as *;

/*=============================
	00. Preloader
===============================*/
.page-revealer {
    pointer-events: none;
    visibility: hidden;
    height: 100%;
    width: 100%;
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    transform: scaleY(0);
    z-index: 12000;
    background-color: var(--tg-common-color-black);
}
.tg-preloader {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--tg-common-color-black);
    z-index: 99999;
    & > .tg-loading {
        display: inline-block;
        position: relative;
        width: 40px;
        height: 40px;
        & > div {
            box-sizing: border-box;
            display: block;
            position: absolute;
            width: 32px;
            height: 32px;
            margin: 4px;
            border: 4px solid transparent;
            border-radius: 50%;
            animation: preloader 1s cubic-bezier(0.5, 0, 0.5, 1) infinite;
            border-color: var(--tg-theme-primary) transparent transparent transparent;
            &:nth-child(1) {
                animation-delay: -0.1s;
            }
            &:nth-child(2) {
                animation-delay: -0.2s;
            }
            &:nth-child(3) {
                animation-delay: -0.3s;
            }
        }
    }
}
@keyframes preloader {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
html.show-preloader body {
    display: none;
}