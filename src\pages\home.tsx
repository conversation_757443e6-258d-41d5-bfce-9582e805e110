import Wrapper from "../layout/wrapper";
import SEOCom from "../components/seo";
import Header from "../layout/header/header";
import HeroBanner from "../components/hero-banner/hero-banner";
// import NftItemArea from "../components/nft-item/nft-item-area";
import AboutArea from "../components/about-area/about-area";
import GalleryArea from "../components/gallery/gallery-area";
import TeamArea from "../components/team/team-area";
// import VideoArea from "../components/video/video-area";
import RoadMapArea from "../components/road-map/road-map-area";
// import TrendingNftItems from "../components/nft-item/trending-nft-items";
import Footer from "../layout/footer/footer";
// import StreamersArea from "../components/streamers/streamers-area";
import SocialArea from "../components/social/social-area";
import ProjectArea from "../components/projects/project-area";
import BrandArea from "../components/brand/brand-area";
import ContactPage from "./contact";
import GithubCalendarSection from "../components/GithubCalendarSection";
import SalaryGraph from "../components/SalaryGraph";
import GitHubRepositories from "../components/GitHubRepositories";

export default function Home() {
  const githubToken = import.meta.env.VITE_GITHUB_TOKEN || "";
  const githubUsername = "BlesslinJerishR";
  return (
    <Wrapper>
      {/* seo title */}
      <SEOCom title="Blesslin Jerish" />
      {/* seo title */}

      {/* header start */}
      <Header />
      {/* header end */}

      {/* main area start */}
      <main className="main--area">
        {/* hero banner start */}
        <HeroBanner />
        {/* hero banner end */}

        {/* nft item area start */}
        {/* <NftItemArea /> */}
        {/* nft item area end */}

        {/* area-background-start */}
        <div
          className="area-background"
          style={{ backgroundImage: `url(/assets/img/bg/area_bg01.jpg})` }}
        >
          <GithubCalendarSection />

          {/* Canva PPT Embed */}
          <div style={}>
            <div
              style={{
                position: "relative",
                width: "100%",
                height: 0,
                paddingTop: "56.2500%",
                paddingBottom: 0,
                boxShadow: "0 2px 8px 0 rgba(63,69,81,0.16)",
                marginTop: "1.6em",
                marginBottom: "0.9em",
                overflow: "hidden",
                borderRadius: "8px",
                willChange: "transform",
                backgroundColor: "#000000",
              }}
            >
              <iframe
                loading="lazy"
                style={{
                  position: "absolute",
                  width: "100%",
                  height: "100%",
                  top: 0,
                  left: 0,
                  border: "none",
                  padding: 0,
                  margin: 0,
                }}
                src="https://www.canva.com/design/DAGqH89YVmc/4X076AslV1gfAcWAKw4fAQ/view?embed&autoplay=1&loop=1"
                allowFullScreen
                allow="fullscreen; autoplay"
              />
            </div>
          </div>

          {/* about-area */}
          <GitHubRepositories token={githubToken} username={githubUsername} />
          {/* <AboutArea /> */}
          {/* about-area-end */}

          {/* gallery area start */}
          {/* <GalleryArea /> */}
          {/* gallery area end */}
        </div>
        {/* area-background-end */}
        {/* <ProjectArea /> */}
        {/* team area start */}
        {/* <TeamArea /> */}
        {/* team area end*/}

        {/* video area start */}
        {/* <VideoArea /> */}
        {/* video area end */}

        {/* road map area start */}
        {/* <RoadMapArea /> */}
        {/* road map area end */}

        {/* trending nft items start */}
        {/* <TrendingNftItems /> */}

        {/* Developers Area */}
        {/* Top Rated Developers */}
        {/* <StreamersArea /> */}

        {/* trending nft items end */}
      </main>
      {/* main area end */}
      {/* <SalaryGraph /> */}

      {/* Clients */}
      {/* <BrandArea /> */}
      {/* <NftItemArea /> */}

      {/* Contact Me */}
      <ContactPage />

      {/* Social Links */}
      <SocialArea />

      {/* footer start */}
      <Footer />
      {/* footer end */}
    </Wrapper>
  );
}
