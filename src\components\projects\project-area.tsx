import { useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import Swiper<PERSON><PERSON> from "swiper/core";
import { Navigation, Scrollbar, Keyboard, Autoplay } from "swiper/modules";
import TextAnimation from "../common/text-animation";
import ImageLightBox from "../common/image-lightbox";

SwiperCore.use([Navigation, Keyboard]);

// slider setting
const slider_setting = {
  speed: 5000,
  autoplay: {
    delay: 2000,
    disableOnInteraction: false,
  },
  slidesPerView: 4,
  spaceBetween: 15,
  loop: true,
  breakpoints: {
    "1500": {
      slidesPerView: 4,
    },
    "1200": {
      slidesPerView: 4,
    },
    "992": {
      slidesPerView: 3,
    },
    "768": {
      slidesPerView: 3,
    },
    "576": {
      slidesPerView: 2,
    },
    "0": {
      slidesPerView: 1.5,
      centeredSlides: true,
      centeredSlidesBounds: true,
    },
  },
  navigation: {
    nextEl: ".slider-button-next",
    prevEl: ".slider-button-prev",
  },
  scrollbar: {
    el: ".scroller",
    draggable: true,
    dragSize: 24,
  },
  keyboard: true,
  onlyInViewport: true,
};

// project data
const project_data = [
  "/assets/img/mobo/1.png",
  "/assets/img/mobo/2.png",
  "/assets/img/mobo/3.png",
  "/assets/img/mobo/4.png",
  "/assets/img/mobo/5.png",
  "/assets/img/mobo/6.png",
  "/assets/img/mobo/7.png",
  "/assets/img/mobo/8.png",
  "/assets/img/mobo/10.png",
  "/assets/img/mobo/11.png",
  "/assets/img/mobo/12.png",
  "/assets/img/mobo/13.png",
  "/assets/img/mobo/14.png",
  "/assets/img/mobo/15.png",
  "/assets/img/mobo/16.png",
  "/assets/img/mobo/17.png",
  "/assets/img/mobo/18.png",
  "/assets/img/mobo/19.png",
  "/assets/img/mobo/20.png",
  "/assets/img/mobo/21.png",
  "/assets/img/mobo/22.png",
  "/assets/img/mobo/23.png",
  "/assets/img/mobo/24.png",
  "/assets/img/mobo/25.png",
];

const ProjectArea = () => {
  // photoIndex
  const [photoIndex, setPhotoIndex] = useState(0);
  // image open state
  const [open, setOpen] = useState(false);
  // images
  const images = project_data.map((img) => img);
  // handleImagePopup
  const handleImagePopup = (index: number) => {
    setPhotoIndex(index);
    setOpen(true);
  };
  return (
    <>
      <section className="project-area project-bg section-pt-120 section-pb-140">
        <div className="container custom-container">
          <div className="project__wrapper">
            <div className="section__title text-start">
              <h3 className="title">MOBILE PROJECTS</h3>
              <TextAnimation title="Past mobile applications i developed" />
              <TextAnimation title="Android & iOS" />
            </div>
            <Swiper
              {...slider_setting}
              modules={[Navigation, Scrollbar, Keyboard, Autoplay]}
              className="swiper-container project-active"
            >
              {project_data.map((p, i) => (
                <SwiperSlide key={i}>
                  <div className="project__item">
                    <a
                      onClick={() => handleImagePopup(i)}
                      className="popup-image cursor-pointer"
                    >
                      <img src={p} alt="img" />
                    </a>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
            <div className="slider-button-prev">
              <i className="flaticon-right-arrow"></i>
              <i className="flaticon-right-arrow"></i>
            </div>
          </div>
        </div>
        <div className="swiper-scrollbar scroller"></div>
      </section>

      {/* image light box start */}
      <ImageLightBox
        images={images}
        visible={open}
        setVisible={setOpen}
        index={photoIndex}
        setIndex={setPhotoIndex}
      />
      {/* image light box end */}
    </>
  );
};

export default ProjectArea;
