import React from "react";
import social_data from "../../data/social-data";
import { NavLink } from "react-router-dom";

// prop type
type IProps = {
  isOffCanvasOpen: boolean;
  setIsOffCanvasOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

const OffCanvas = ({ isOffCanvasOpen, setIsOffCanvasOpen }: IProps) => {
  // handle close search
  const handleCloseOffCanvas = (audioPath: string) => {
    setIsOffCanvasOpen(false);
    const audio = new Audio(audioPath);
    audio.play();
  };
  return (
    <div className={`${isOffCanvasOpen ? "offCanvas__menu-visible" : ""}`}>
      <div className="offCanvas__wrap">
        <div className="offCanvas__body">
          <div className="offCanvas__top">
            <div className="offCanvas__logo logo">
              <NavLink to="/">
                <img src="/assets/img/logo/logo.png" alt="Logo" />
              </NavLink>
            </div>
            <div
              className="offCanvas__toggle"
              onClick={() => handleCloseOffCanvas("/assets/audio/remove.wav")}
            >
              {/* <i className="flaticon-swords-in-cross-arrangement"></i> */}
              <img
                src="/assets/img/favicon.png"
                alt="nothing"
                style={{ backgroundColor: "#FFFFFF", borderRadius: 9999 }}
              />
            </div>
          </div>
          <div className="offCanvas__content">
            <h2 className="title">
              who writes the code ? <span>posterity_</span>
            </h2>
            <div className="offCanvas__contact">
              <h4 className="small-title">CONTACT ME</h4>
              <ul className="offCanvas__contact-list list-wrap">
                <li>
                  <NavLink to="tel:************">+91 8072981518</NavLink>
                </li>
                <li>
                  <NavLink to="mailto:<EMAIL>">
                    <EMAIL>
                  </NavLink>
                </li>
                <li>Remote, India</li>
              </ul>
            </div>
            <div className="offCanvas__newsletter">
              <h4 className="small-title">Want to Hire me ?</h4>
              <form
                action="https://formspree.io/f/xeqwrjjn"
                method="POST"
                className="offCanvas__newsletter-form"
              >
                <input
                  type="email"
                  placeholder="Your email address"
                  name="email"
                  required
                />
                <button type="submit">
                  <i className="flaticon-send"></i>
                </button>
              </form>
              <p>Blesslin Jerish will contact you soon.</p>
            </div>
            <ul className="offCanvas__social list-wrap">
              {social_data.map((s, i) => (
                <li key={i}>
                  <NavLink to={s.link} target="_blank">
                    <i className={s.icon}></i>
                  </NavLink>
                </li>
              ))}
            </ul>
          </div>
          <div className="offCanvas__copyright">
            <p>
              Copyright © {new Date().getFullYear()} - <span>Blesslin.</span>
            </p>
          </div>
        </div>
      </div>
      <div
        onClick={() => setIsOffCanvasOpen(false)}
        className="offCanvas__overlay"
      ></div>
    </div>
  );
};

export default OffCanvas;
