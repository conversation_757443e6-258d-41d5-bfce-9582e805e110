import { useState } from "react";
import { InView } from "react-intersection-observer";
import useTextAnimation from "../../hooks/use-text-animation";
import { NavLink } from "react-router-dom";

// road map lists
type IRoadMap = {
  id: number;
  active: boolean;
  title: string;
  lists: {
    active: boolean;
    text: string;
  }[];
};
const road_map_lists: IRoadMap[] = [
  {
    id: 1,
    active: true,
    title: "Mercor, USA",
    lists: [
      {
        active: true,
        text: "Delivered a minimum viable product (MVP) of Ai powered news software within an aggressive 3-day timeframe",
      },
      {
        active: true,
        text: "Seamlessly integrated APIs from OpenAI, Perplexity, Supabase, Twilio, and Elevelabs to enhance software functionality",
      },
      {
        active: true,
        text: "Crafted intuitive user and admin interface systems, ensuring a seamless user experience",
      },
      {
        active: true,
        text: "Implemented robust create, read, update, and delete (CRUD) operations in Supabase using NodeJS, and ExpressJS",
      },
      {
        active: true,
        text: "Developed whatsapp business API bot equipped with core features from admin work flow.",
      },
    ],
  },
  {
    id: 2,
    active: false,
    title: "Kynection, Australia",
    lists: [
      {
        active: true,
        text: "Developed the Firefly Initiative, a hybrid Android, iOS, and Web app, using React Native",
      },
      {
        active: true,
        text: "Designed and built a Volunteering Time validation app, delivering a seamless user experience",
      },
      {
        active: true,
        text: "Crafted 20+ screens with rich UI/UX using React Native, significantly enhancing overall user engagement",
      },
      {
        active: true,
        text: "Established seamless database connectivity by integrating MongoDB, Supabase, and ExpressJS",
      },
      {
        active: true,
        text: "Thrived in a remote working environment for the Australia shift, ensuring uninterrupted workflow and productivity",
      },
      {
        active: true,
        text: "Pioneered innovation by developing prototypes of the core hybrid mobile app using Visily.ai",
      },
    ],
  },
  {
    id: 3,
    active: false,
    title: "Brandidea, India",
    lists: [
      {
        active: true,
        text: "Designed and developed multiple e-commerce websites, including a Shopify-based Tee shirts eCommerce website and a custom WordPress website for Artee Kids",
      },
      {
        active: true,
        text: "Integrated Razorpay payment gateway and Web3Forms API to enhance website functionality, ensuring seamless transactions and improved user experience",
      },
      {
        active: true,
        text: "Collaborated with cross-functional design and marketing teams to ensure timely and successful project execution, driving business growth through effective teamwork",
      },
    ],
  },
  {
    id: 4,
    active: false,
    title: "Zoho, India",
    lists: [
      {
        active: true,
        text: "Developed practical applications, including web applications, games, and software, utilizing core programming languages and technology stacks to deliver tangible solutions",
      },
      {
        active: true,
        text: "Improved problem-solving skills through competitive coding challenges, enhancing coding proficiency",
      },
    ],
  },
];

const RoadMapArea = () => {
  const [isView, setIsView] = useState<boolean>(false);
  useTextAnimation(isView);

  const handleInViewChange = (inView: boolean) => {
    if (inView) {
      setIsView(true);
    }
  };

  return (
    <section
      className="roadMap__area roadMap-bg section-pt-150 section-pb-150"
      // style={{ backgroundImage: `url(/assets/img/bg/roadmap_bg.jpg)` }}
    >
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-xl-10">
            <div className="roadMap__inner">
              <div className="row">
                <div className="col-xl-5 col-lg-6">
                  <div className="roadMap__content">
                    <h2 className="title">Experience Roadmap</h2>
                    <p>
                      Just a kid with a laptop who thinks he can change the
                      world.
                    </p>
                    <NavLink to="/contact" className="tg-btn-1 -btn-yellow">
                      <span>Hire me ?</span>
                    </NavLink>
                  </div>
                  <div className="roadMap__img">
                    <img
                      src="/assets/img/exp/8.png"
                      className="tg-parallax"
                      data-scale="1.5"
                      data-orientation="down"
                      alt="roadMap__img"
                    />
                  </div>
                  <div className="roadMap__img tx">
                    <img
                      src="/assets/img/exp/9.png"
                      className="tg-parallax"
                      data-scale="1.5"
                      data-orientation="down"
                      alt="roadMap__img"
                    />
                  </div>
                  <div className="roadMap__img tx">
                    <img
                      src="/assets/img/exp/10.png"
                      className="tg-parallax"
                      data-scale="1.5"
                      data-orientation="down"
                      alt="roadMap__img"
                    />
                  </div>
                  <div className="roadMap__img tx">
                    <img
                      src="/assets/img/exp/7.png"
                      className="tg-parallax"
                      data-scale="1.5"
                      data-orientation="down"
                      alt="roadMap__img"
                    />
                  </div>
                </div>
                <div className="col-xl-7 col-lg-6">
                  <div className="roadMap__steps-wrap">
                    {road_map_lists.map((item) => (
                      <div
                        key={item.id}
                        className={`roadMap__steps-item ${
                          item.active ? "active" : ""
                        }`}
                      >
                        <h3 className="title">{item.title}</h3>
                        <InView
                          as="ul"
                          onChange={handleInViewChange}
                          className="roadMap__list list-wrap"
                        >
                          {item.lists.map((l, i) => (
                            <li
                              key={i}
                              className={`${
                                l.active ? "active" : ""
                              } tg__animate-text style2`}
                            >
                              {l.text}
                            </li>
                          ))}
                        </InView>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RoadMapArea;
