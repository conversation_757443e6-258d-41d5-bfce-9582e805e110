import { NavLink } from "react-router-dom";
import TextAnimation from "../common/text-animation";

// social item
const social_data: {
  id: number;
  link: string;
  icon: string;
  title: string;
}[] = [
  {
    id: 1,
    link: "mailto:<EMAIL>",
    icon: "fa fa-envelope",
    title: "Gmail",
  },
  {
    id: 2,
    link: "https://wa.link/wzfw6f",
    icon: "fab fa-whatsapp",
    title: "Whatsapp",
  },
  {
    id: 3,
    link: "https://www.linkedin.com/in/blesslinjerish",
    icon: "fab fa-linkedin-in",
    title: "LinkedIn",
  },
  {
    id: 4,
    link: "https://www.instagram.com/blessl.in",
    icon: "flaticon-instagram",
    title: "instagram",
  },
];

const SocialArea = () => {
  return (
    <section
      className="social__area social-bg"
      style={{ backgroundImage: `url(/assets/img/bg/social_bg.png)` }}
    >
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-xl-6 col-lg-7 col-md-10">
            <div className="section__title text-center mb-60">
              <TextAnimation title="connect with me" />
              <h3 className="title">stay connected</h3>
            </div>
          </div>
        </div>
        <div className="row justify-content-center gutter-20 row-cols-2 row-cols-lg-6 row-cols-md-4 row-cols-sm-3">
          {social_data.map((s, i) => (
            <div key={i} className="col">
              <div className="social__item">
                <NavLink to={s.link} target="_blank">
                  <i className={s.icon}></i>
                  <span>{s.title}</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="65"
                    height="5"
                    viewBox="0 0 65 5"
                  >
                    <path
                      d="M968,5630h65l-4,5H972Z"
                      transform="translate(-968 -5630)"
                    />
                  </svg>
                </NavLink>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SocialArea;
