import { NavLink } from "react-router-dom";

const Footer = () => {
  return (
    <footer className="footer-style-one">
      <div className="footer__top-wrap">
        <div className="container">
          <div className="row">
            <div className="col-xl-4 col-lg-5 col-md-7">
              <div className="footer-widget">
                <div className="footer-logo logo">
                  <NavLink to="/contact">
                    <img src="/assets/img/logo/logo.png" alt="Logo" />
                  </NavLink>
                </div>
                <div className="footer-text">
                  <p className="desc px-4">
                    blessl.in
                    <br />
                    Dev<PERSON><PERSON> with 6+ years of experience specializes in
                    creating innovative solutions for the digital era.
                  </p>
                  {/* <p className="social-title">
                    Active{" "}
                    <span>
                      With Us <i className="fas fa-angle-double-right"></i>
                    </span>
                  </p> */}
                  <div className="footer-social px-4">
                    {/* <NavLink to="#">
                      <img
                        src="/assets/img/icons/social_icon01.png"
                        alt="iocn"
                      />
                    </NavLink>
                    <NavLink to="#">
                      <img
                        src="/assets/img/icons/social_icon02.png"
                        alt="iocn"
                      />
                    </NavLink> */}
                    <NavLink to="https://linkedin.com/in/blesslinjerish">
                      <img
                        src="/assets/img/icons/l.png"
                        width={50}
                        alt="icon"
                      />
                    </NavLink>
                    <NavLink to="#">
                      <img
                        src="/assets/img/icons/social_icon03.png"
                        alt="icon"
                        width={40}
                        style={{ marginTop: "5px" }}
                      />
                    </NavLink>
                    {/* <NavLink to="#">
                      <img
                        src="/assets/img/icons/social_icon04.png"
                        alt="iocn"
                      />
                    </NavLink> */}
                  </div>
                </div>
              </div>
            </div>
            <div className="col-xl-2 col-lg-3 col-md-5 col-sm-6 px-5">
              <div className="footer-widget widget_nav_menu">
                <h4 className="fw-title">Services</h4>
                <ul className="list-wrap menu" style={{ width: "250%" }}>
                  <li>
                    <NavLink to="#">○ Web Development</NavLink>
                  </li>
                  <li>
                    <NavLink to="#">○ Wordpress Development</NavLink>
                  </li>
                  <li>
                    <NavLink to="#">○ Shopify Development</NavLink>
                  </li>
                  <li>
                    <NavLink to="#">○ Web App Development</NavLink>
                  </li>
                  <li>
                    <NavLink to="#">○ Mobile App Development</NavLink>
                  </li>
                  <li>
                    <NavLink to="#">○ SaaS Development</NavLink>
                  </li>
                </ul>
              </div>
            </div>
            <div className="col-xl-2 col-lg-3 col-md-5 col-sm-6 px-4">
              <div
                className="footer-widget widget_nav_menu mlx"
                style={{ marginLeft: "3vw" }}
              >
                <h4 className="fw-title">Support</h4>
                <ul className="list-wrap menu">
                  <li>
                    <NavLink to="#">FAQ</NavLink>
                  </li>
                  <li>
                    <NavLink to="#">Privacy</NavLink>
                  </li>
                  {/* <li>
                    <NavLink to="#">Live Auctions</NavLink>
                  </li>
                  <li>
                    <NavLink to="#">Item Details</NavLink>
                  </li> */}
                  <li>
                    <NavLink to="#">24/7 Support</NavLink>
                  </li>
                  {/* <li>
                    <NavLink to="#">Our News</NavLink>
                  </li> */}
                </ul>
              </div>
            </div>
            <div className="col-xl-4 col-lg-5 col-md-7 px-5">
              <div className="footer-widget">
                <h4 className="fw-title">Hire me ?</h4>
                <div className="footer-newsletter">
                  <p>Blesslin Jerish will contact you soon</p>
                  <form
                    action="https://formspree.io/f/xeqwrjjn"
                    method="POST"
                    className="footer-newsletter-form"
                  >
                    <input
                      type="email"
                      placeholder="Your email address"
                      name="email"
                      required
                    />
                    <button type="submit">
                      <i className="flaticon-paper-plane"></i>
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="copyright__wrap">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-md-7">
              <div className="copyright__text">
                <p>
                  Copyright © {new Date().getFullYear()} - All Rights Reserved
                  <br />
                  By <span>Blesslin.</span>
                </p>
              </div>
            </div>
            {/* <div className="col-md-5">
              <div className="copyright__card text-center text-md-end">
                <img src="/assets/img/others/payment_card.png" alt="img" />
              </div>
            </div> */}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
