import { useState } from "react";
import { NavLink } from "react-router-dom";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import TextAnimation from "../common/text-animation";

// slider setting
const slider_setting = {
  dots: false,
  infinite: true,
  speed: 500,
  autoplay: true,
  arrows: false,
  slidesToShow: 3,
  slidesToScroll: 0,
  responsive: [
    {
      breakpoint: 1200,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 0,
        infinite: true,
      },
    },
    {
      breakpoint: 992,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 0,
      },
    },
    {
      breakpoint: 767,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 0,
        arrows: false,
      },
    },
    {
      breakpoint: 575,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
      },
    },
  ],
};

// brands
const brands = [
  "/assets/img/exp/11.png",
  "/assets/img/exp/12.png",
  "/assets/img/exp/13.png",
];
const BrandArea = () => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  return (
    <section className="brand-area" style={{ marginTop: -200 }}>
      <div className="container">
        <div className="row">
          <div className="col-12">
            <div className="section__title text-center mb-60">
              <TextAnimation title="People I have worked" />
              <TextAnimation title="with comes from" />
            </div>
          </div>
        </div>
        <div id="contact"></div>
        <Slider {...slider_setting} className="row brand-active">
          {brands.map((b, i) => (
            <div key={i} className="col">
              <div
                className="brand__item"
                onMouseEnter={() => setHoveredIndex(i)}
                onMouseLeave={() => setHoveredIndex(null)}
                style={{
                  opacity:
                    hoveredIndex === null || hoveredIndex === i ? 1 : 0.3,
                  transition: "opacity 0.3s",
                  marginTop: 50,
                }}
              >
                <NavLink to="#" className="brand__link">
                  <img src={b} alt="brand" style={{ height: "30vh" }} />
                </NavLink>
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </section>
  );
};

export default BrandArea;
