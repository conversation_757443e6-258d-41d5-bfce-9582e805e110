import { useContext, useEffect, useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import {
  Navigation,
  Scrollbar,
  Pagination,
  Keyboard,
  Autoplay,
} from "swiper/modules";
import gallery_data from "../../data/gallery-data";
import ImageLightBox from "../common/image-lightbox";
import { AppContext } from "../../context/app-context";
import SwiperCore from "swiper/core";

SwiperCore.use([Navigation, Keyboard]);

// slider setting
const slider_setting = {
  infinite: true,
  speed: 5000,
  autoplay: {
    delay: 2000,
    disableOnInteraction: false,
  },
  centeredSlides: true,
  centeredSlidesBounds: true,
  spaceBetween: 30,
  loop: true,
  freeMode: false,
  observer: true,
  observeParents: true,
  breakpoints: {
    1920: {
      slidesPerView: 1,
    },
    992: {
      slidesPerView: 1,
    },
    320: {
      slidesPerView: 1,
    },
  },
  navigation: {
    nextEl: ".swiper-button-next",
    prevEl: ".swiper-button-prev",
  },
  scrollbar: {
    el: ".swiper-scrollbar",
    draggable: true,
  },
};
const GalleryArea = () => {
  const galleryRef = useRef<HTMLDivElement | null>(null);

  const { handleMouseEnter, handleMouseLeave } = useContext(AppContext);
  // photoIndex
  const [photoIndex, setPhotoIndex] = useState(0);
  // image open state
  const [open, setOpen] = useState(false);
  // images
  const images = gallery_data.map((item) => item.img);
  // handleImagePopup
  const handleImagePopup = (index: number) => {
    setPhotoIndex(index);
    setOpen(true);
  };

  const handleNextSlide = () => {
    const swiperContainer = document.querySelector(".swiper-container") as any;
    if (swiperContainer) {
      const swiper = swiperContainer.swiper;
      if (swiper) {
        swiper.slideNext();
      }
    }
  };

  const handlePrevSlide = () => {
    const swiperContainer = document.querySelector(".swiper-container") as any;
    if (swiperContainer) {
      const swiper = swiperContainer.swiper;
      if (swiper) {
        swiper.slidePrev();
      }
    }
  };

  useEffect(() => {
    const handleKeyDown = (event: any) => {
      if (event.key === "ArrowRight") {
        handleNextSlide();
      } else if (event.key === "ArrowLeft") {
        handlePrevSlide();
      }
    };

    const currentElement = galleryRef.current!;
    if (currentElement) {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            document.addEventListener("keydown", handleKeyDown);
          } else {
            document.removeEventListener("keydown", handleKeyDown);
          }
        },
        { threshold: 0.5 } // Adjust threshold as needed
      );

      observer.observe(currentElement);

      return () => {
        document.removeEventListener("keydown", handleKeyDown);
        observer.disconnect();
      };
    }
  }, [handleNextSlide, handlePrevSlide]);

  return (
    <>
      <section className="gallery__area fix section-pb-130" ref={galleryRef}>
        <div className="gallery__slider">
          <div className="container">
            <div className="row justify-content-center">
              <div className="col-xl-9 col-lg-10 col-md-11">
                <Swiper
                  {...slider_setting}
                  modules={[Navigation, Scrollbar, Pagination, Autoplay]}
                  className="swiper-container gallery-active"
                  centeredSlides={true}
                  observer={true}
                  observeParents={true}
                  initialSlide={1}
                >
                  {gallery_data.map((item, i) => (
                    <SwiperSlide key={item.id}>
                      <div className="gallery__item">
                        <div className="gallery__thumb">
                          <a
                            data-cursor="-theme"
                            data-cursor-text="View <br> Image"
                            className="popup-image cursor-pointer"
                            title={item.title}
                            onClick={() => handleImagePopup(i)}
                            onMouseEnter={handleMouseEnter}
                            onMouseLeave={handleMouseLeave}
                          >
                            <img
                              src={item.img}
                              alt="img"
                              className="gallery-img"
                            />
                          </a>
                        </div>
                        <div className="gallery__content">
                          <h3 className="title">{item.title}</h3>
                          <span className="rate">{item.rate}</span>
                        </div>
                      </div>
                    </SwiperSlide>
                  ))}
                  <div className="swiper-scrollbar"></div>
                </Swiper>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* image light box start */}
      <ImageLightBox
        images={images}
        visible={open}
        setVisible={setOpen}
        index={photoIndex}
        setIndex={setPhotoIndex}
      />
      {/* image light box end */}
    </>
  );
};

export default GalleryArea;
