@use '../utils' as *;

/*=============================
    00. Accordion
===============================*/
.faq {
    &-area {
        background-color: #0a0e13;
        padding: 120px 0;
    }
    &__content-wrap {
        & .section__title {
            margin: 0 0 55px;
            &::after {
                display: none;
            }
            & .title {
                margin: 0 0 24px;
            }
            & p {
                margin: 0 0;
                @media #{$lg} {
                    & br {
                        display: none;
                    }
                }
            }
        }
    }
    &__wrapper {
        & .accordion-item {
            background: transparent;
            border: none;
            border-radius: 0;
            padding-bottom: 35px;
            margin-bottom: 35px;
            border-bottom: 1px solid #202428;
            @media #{$lg} {
                padding-bottom: 25px;
                margin-bottom: 25px;
            }
            &:last-child {
                margin: 0 0;
            }
        }
        & .accordion-header {
            margin: 0 0;
        }
        & .accordion-button {
            padding: 0 0;
            background: transparent;
            border-radius: 0 !important;
            box-shadow: none;
            color: var(--tg-common-color-white);
            font-size: 20px;
            font-weight: var(--tg-fw-semi-bold);
            letter-spacing: 1px;
            border: none !important;
            box-shadow: none;
            &::after {
                content: "\f067";
                background-image: none !important;
                font-size: 16px;
                font-family: var(--tg-icon-font-family);
                color: var(--tg-body-color);
                opacity: .76;
                width: auto;
                height: auto;
            }
            & .count {
                width: 29px;
                height: 29px;
                @include flexbox();
                align-items: center;
                justify-content: center;
                font-size: 16px;
                color: var(--tg-common-color-black);
                background: var(--tg-body-color);
                font-weight: var(--tg-fw-extra-bold);
                @include border-radius(50%);
                margin-right: 16px;
                @include transition(.3s);
            }
            &:hover {
                color: var(--tg-theme-primary);
                & .count {
                    background: var(--tg-theme-primary);
                }
            }
            &:not(.collapsed) {
                &::after {
                    content: "\f068";
                }
                & .count {
                    background: var(--tg-theme-primary);
                }
            }
        }
        & .accordion-body {
            padding: 11px 0 0 45px;
            @media #{$lg} {
                padding: 15px 0 0 0;
            }
            @media #{$md} {
                padding: 11px 0 0 45px;
            }
            @media #{$xs} {
                padding: 15px 0 0 0;
            }
        }
    }
}