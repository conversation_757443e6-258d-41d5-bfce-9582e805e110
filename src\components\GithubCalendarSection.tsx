import { useState } from "react";
import GitHubCalendar from "react-github-calendar";

const GithubCalendarSection = () => {
  const currentYear = new Date().getFullYear();
  const [selectedYear, setSelectedYear] = useState<"last" | number>("last");

  const years = Array.from(
    { length: currentYear - 2020 },
    (_, i) => 2021 + i
  ).reverse();

  return (
    <div
      className="area-background"
      style={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        padding: "3vh",
      }}
    >
      <div style={{ marginBottom: "50px", display: "flex", gap: "30px" }}>
        <button
          onClick={() => setSelectedYear("last")}
          style={{
            backgroundColor: "#000",
            borderLeft: 0,
            borderRight: 0,
            borderTop: 0,
            borderBottom: "1.5px solid #45F882",
            borderRadius: 7,
            width: "7vw",
          }}
          className="gb"
        >
          365 days
        </button>
        {years.map((year) => (
          <button
            key={year}
            onClick={() => setSelectedYear(year)}
            style={{
              backgroundColor: "#000",
              borderLeft: 0,
              borderRight: 0,
              borderTop: 0,
              borderBottom: "1.5px solid #45F882",
              borderRadius: 7,
              width: "5vw",
            }}
            className="gb"
          >
            {year}
          </button>
        ))}
      </div>
      <GitHubCalendar
        username="blesslinjerishr"
        style={{
          textAlign: "center",
          paddingBottom: "5vh",
        }}
        year={selectedYear}
      />
    </div>
  );
};

export default GithubCalendarSection;
