@use "../utils" as *;
/*=============================
    03. Slider
===============================*/
.slider {
  &__bg {
    background-position: center;
    background-size: cover;
    padding: 158px 0 54px;
    position: relative;
    z-index: 1;
    @media #{$md} {
      padding: 175px 0 55px;
    }
    @media #{$xs} {
      padding: 160px 0 55px;
    }
  }
  &__content {
    margin: 135px 0 0 40px;
    position: relative;
    z-index: 2;
    @media #{$xxl} {
      margin: 135px 0 0 0;
    }
    @media #{$xl} {
      margin: 90px 0 0 0;
    }
    @media #{$md} {
      margin: 0 0 70px;
      text-align: center;
    }
    @media #{$xs} {
      margin: 0 0 50px;
    }
    & .sub-title {
      margin: 0 0 19px;
      line-height: 1;
      font-family: var(--tg-body-font-family);
      font-weight: var(--tg-fw-bold);
      letter-spacing: 4px;
      font-size: 20px;
      color: var(--tg-theme-primary);
      position: relative;
      padding: 17px 0 17px 20px;
      @media #{$lg} {
        font-size: 16px;
        padding: 15px 0 15px 20px;
      }
      @media #{$md} {
        font-size: 18px;
        padding: 15px 20px 15px;
      }
      @media #{$xs} {
        font-size: 17px;
        padding: 15px 20px 15px;
      }
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        background-image: url(/assets/img/slider/text_gradient_bg.png);
        width: 100%;
        height: 100%;
        background-repeat: no-repeat;
        z-index: -1;
        opacity: 0.28;
        @media #{$lg} {
          background-size: contain;
        }
        @media #{$md} {
          left: 86px;
          top: 0;
          right: 0;
          width: 297px;
          margin: 0 auto;
        }
        @media #{$xs} {
          left: 55px;
          width: 256px;
          margin: 0 auto;
        }
      }
    }
    & .title {
      margin: 0 0 21px;
      font-size: 100px;
      //   font-family: var(--tg-berlin-font-family);
      //   line-height: 0.8;
      text-transform: capitalize;
      text-shadow: -1px 5px 0px rgb(69 248 130 / 66%);
      @media #{$xl} {
        font-size: 118px;
      }
      @media #{$lg} {
        font-size: 94px;
      }
      @media #{$md} {
        font-size: 120px;
      }
      @media #{$xs} {
        font-size: 18vw;
        text-shadow: -1px 3px 0px rgb(69 248 130 / 66%);
      }
      @media #{$sm} {
        font-size: 17vw;
        text-shadow: -1px 5px 0px rgb(69 248 130 / 66%);
      }
    }
    & p {
      margin: 0 0;
      font-size: 26px;
      text-transform: uppercase;
      font-weight: 700;
      letter-spacing: 5px;
      line-height: 1.4;
      color: var(--tg-common-color-white);
      @media #{$lg} {
        font-size: 22px;
      }
      @media #{$md} {
        font-size: 24px;
      }
      @media #{$xs} {
        font-size: 20px;
        letter-spacing: 3px;
      }
      @media #{$sm} {
        font-size: 24px;
        letter-spacing: 5px;
      }
    }
  }
  &__btn {
    margin: 33px 0 0;
    & .tg-btn-1 {
      padding: 15px 49px;
    }
  }
  &__img {
    & img {
      margin: 0 auto;
      @media #{$lg} {
        margin: 0 0 0 auto;
        max-width: 90%;
        display: block;
      }
      @media #{$md} {
        margin: 0 auto;
        max-width: 480px;
        width: 100%;
        display: block;
      }
    }
  }
  &__shapes {
    opacity: 0.27;
    & img {
      position: absolute;
      animation: shapeBlinker 1.5s linear infinite;
      opacity: 0;
      z-index: -1;
      &:nth-child(1) {
        left: 23%;
        top: 17%;
        animation-delay: 0.2s;
        @media #{$md} {
          left: 13%;
          top: 12%;
        }
        @media #{$xs} {
          left: 11%;
          top: 11%;
        }
      }
      &:nth-child(2) {
        left: 29%;
        bottom: 45%;
        animation-delay: 0.4s;
        @media #{$md} {
          left: 12%;
        }
        @media #{$xs} {
          left: 10%;
          bottom: 55%;
        }
      }
      &:nth-child(3) {
        right: 47%;
        top: 20%;
        animation-delay: 0.2s;
        @media #{$md} {
          right: 23%;
          top: 15%;
        }
        @media #{$xs} {
          right: 10%;
          top: 17%;
        }
      }
      &:nth-child(4) {
        right: 42%;
        top: 23%;
        animation-delay: 0.6s;
        @media #{$xl} {
          right: 40%;
          top: 28%;
        }
        @media #{$md} {
          right: 20%;
          top: 35%;
        }
        @media #{$xs} {
          right: 14%;
          top: 33%;
        }
      }
    }
  }
  &__brand {
    &-wrap {
      position: absolute;
      left: 0;
      bottom: 130px;
      width: 100%;
      height: auto;
      @media #{$xl} {
        bottom: 150px;
      }
      @media #{$lg} {
        bottom: 85px;
      }
      @media #{$md} {
        bottom: 0;
        position: relative;
      }
    }
    &-list {
      display: flex;
      flex-wrap: wrap;
      width: 50%;
      align-items: center;
      gap: 20px 60px;
      margin: 0 0 0 40px;
      @media #{$xxl} {
        margin: 0 0 0;
      }
      @media #{$lg} {
        width: 60%;
        gap: 20px 30px;
      }
      @media #{$md} {
        width: 100%;
        justify-content: center;
      }
      @media #{$xs} {
        flex-wrap: nowrap;
        gap: 20px;
      }
      @media #{$sm} {
        gap: 35px;
      }
      & li {
        & a {
          display: block;
        }
      }
    }
  }
  &__area {
    position: relative;
    &::before,
    &::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 50%;
      -webkit-clip-path: polygon(0 0, 0 100%, 100% 100%);
      clip-path: polygon(0 0, 0 100%, 100% 100%);
      background-color: var(--tg-theme-primary);
      height: 50px;
      @media #{$xl} {
        height: 40px;
      }
      @media #{$lg} {
        height: 30px;
      }
      @media #{$xs} {
        height: 20px;
      }
    }
    &::after {
      left: auto;
      right: 0;
      -webkit-clip-path: polygon(100% 0, 0 100%, 100% 100%);
      clip-path: polygon(100% 0, 0 100%, 100% 100%);
    }
  }
}
.banner {
  &__padding {
    padding: 310px 0 295px;
    position: relative;
    z-index: 1;
    @media #{$xl} {
      padding: 260px 0 220px;
    }
    @media #{$xs} {
      padding: 180px 0 150px;
      min-height: 100vh;
      display: flex;
      align-items: center;
    }
  }
  &__bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-position: center;
    background-size: cover;
    z-index: -1;
  }
  &__content {
    margin: 0 0;
    & .title {
      @media #{$xl} {
        font-size: 136px;
      }
      @media #{$md} {
        font-size: 118px;
      }
      @media #{$xs} {
        font-size: 18vw;
      }
      @media #{$sm} {
        font-size: 17vw;
      }
    }
  }
  &__btn {
    margin: 33px 0 0;
  }
}
