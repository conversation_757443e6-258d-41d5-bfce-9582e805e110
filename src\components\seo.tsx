import { Helmet } from "react-helmet-async";

// prop type
type IProps = {
  title: string;
};
const SEOCom = ({ title }: IProps) => {
  return (
    <Helmet>
      <meta charSet="utf-8" />
      <title>
        {title
          ? `<PERSON><PERSON><PERSON> Jerish ◉ who writes the code ? posterity_`
          : "◉ who writes the code ? posterity_"}
      </title>
      <meta name="robots" content="noindex, follow" />
      <meta
        name="description"
        content="<PERSON><PERSON><PERSON> Jerish ◉ who writes the code ? posterity_"
      />
      <meta
        name="viewport"
        content="width=device-width, initial-scale=1, shrink-to-fit=no"
      />
    </Helmet>
  );
};

export default SEOCom;
