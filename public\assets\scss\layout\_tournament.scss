@use '../utils' as *;

/*=============================
    14. Tournament
===============================*/
.tournament {
    &-area {
        background: var(--tg-common-color-black-4);
    }
    &__wrapper {
        padding: 0 80px;
        @media #{$xl} {
            padding: 0 60px;
        }
        @media #{$lg} {
            padding: 0 0;
        }
        & .gutter-25 {
            --bs-gutter-x: 25px;
            @media #{$lg} {
                --bs-gutter-x: 30px;
            }
        }
    }
    &__box {
        &-wrap {
            padding: 25px 30px 50px;
            position: relative;
            overflow: hidden;
            @include border-radius(0 0 17px 17px);
            transition: .4s ease;
            margin: 0 0 30px;
            z-index: 1;
            &:hover {
                @include transform(translateY(-7px));
            }
            @media #{$md} {
                background: #19222b;
                padding: 25px 30px 40px;
                @include border-radius(17px);
                border: 1px solid #212d38;
                &:hover {
                    @include transform(translateY(0));
                }
            }
            &.active {
                --tg-theme-primary: var(--tg-theme-secondary);
            }
            &::before {
                content: "";
                position: absolute;
                left: 50%;
                top: 20%;
                transform: translateX(-50%);
                background: var(--tg-theme-primary);
                background: radial-gradient(circle, var(--tg-theme-primary) 0%, transparent 100%);
                width: 90px;
                height: 90px;
                filter: blur(50px);
                @include transition(.3s);
                z-index: -1;
            }
            & .main-bg {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: auto;
                z-index: -2;
                @media #{$md} {
                    display: none;
                }
            }
            & .price-bg {
                position: absolute;
                left: 0;
                top: 0;
                width: 166px;
                height: 56px;
                color: var(--tg-theme-primary);
                z-index: -1;
                pointer-events: none;
            }
        }
        &-price {
            position: absolute;
            left: 0;
            top: 0;
            @include flexbox();
            align-items: center;
            gap: 7px;
            font-family: var(--tg-heading-font-family);
            font-weight: var(--tg-fw-extra-bold);
            color: var(--tg-common-color-black);
            font-size: 20px;
            line-height: 1;
            padding: 17px 17px;
            letter-spacing: 1px;
            z-index: 2;
            & i {
                font-size: 18px;
                letter-spacing: 0;
            }
        }
        &-countdown {
            margin: 0 0 46px;
            & .coming-time {
                @include flexbox();
                justify-content: flex-end;
                gap: 15px;
                line-height: 1;
                text-align: center;
            }
            & .time-count {
                line-height: 1;
                font-size: 13px;
                font-family: var(--tg-heading-font-family);
                text-transform: uppercase;
                font-weight: var(--tg-fw-semi-bold);
                color: #888888;
                position: relative;
                &::after {
                    content: ":";
                    position: absolute;
                    top: 0;
                    right: -10px;
                    font-size: 14px;
                }
                &:last-child {
                    &::after {
                        display: none;
                    }
                }
                & span {
                    display: block;
                    color: var(--tg-common-color-white);
                    font-size: 14px;
                    margin: 0 0 7px;
                }
            }
        }
        &-caption {
            text-align: center;
            margin: 0 0 28px;
            & .sub {
                display: block;
                font-size: 16px;
                line-height: 1;
                font-family: var(--tg-heading-font-family);
                color: var(--tg-common-color-white);
                font-weight: var(--tg-fw-extra-bold);
                letter-spacing: 1px;
                margin: 0 0 5px;
            }
            & .title {
                font-size: 30px;
                font-weight: var(--tg-fw-extra-bold);
                margin: 0 0;
                color: var(--tg-theme-primary);
                letter-spacing: 1px;
            }
        }
        &-prize {
            text-align: center;
            background: var(--tg-common-color-black-4);
            @include border-radius(10px);
            font-size: 18px;
            text-transform: uppercase;
            font-family: var(--tg-heading-font-family);
            font-weight: var(--tg-fw-bold);
            letter-spacing: 1px;
            @include flexbox();
            align-items: center;
            justify-content: center;
            gap: 0 13px;
            padding: 24px 20px;
            border: 1px solid #1e2933;
            margin: 0 0 40px;
            & i {
                color: var(--tg-theme-secondary);
                font-size: 16px;
            }
        }
        &-list {
            & li {
                margin: 0 0 20px;
                &:last-child {
                    margin: 0 0;
                }
            }
            &-item {
                @include flexbox();
                align-items: center;
                &:hover {
                    & .tournament__player-name {
                        color: var(--tg-common-color-white);
                    }
                }
            }
        }
    }
    &__player {
        &-thumb {
            flex: 0 0 auto;
            width: 40px;
            margin-right: 20px;
            & img {
                @include border-radius(6px);
            }
        }
        &-name {
            font-size: 14px;
            margin: 0 0;
            color: var(--tg-body-color);
            @include transition(.3s);
            letter-spacing: 1px;
        }
        &-price {
            margin-left: auto;
            color: var(--tg-theme-primary);
            font-family: var(--tg-heading-font-family);
            font-weight: var(--tg-fw-semi-bold);
            letter-spacing: 1px;
            & i {
                color: var(--tg-theme-secondary);
                margin-left: 7px;
                font-size: 14px;
            }
        }
    }
    &__list {
        &-area {
            background-position: center;
            background-size: cover;
        }
        &-item {
            position: relative;
            margin: 0 0 25px;
            z-index: 1;
            @media #{$md} {
                background: var(--tg-border-1);
                margin: 0 0;
                padding: 30px;
                @include border-radius(10px);
                border: 1px solid #212d38;
                &-wrapper {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 30px;
                }
            }
            @media #{$xs} {
                &-wrapper {
                    grid-template-columns: repeat(1, 1fr);
                }
            }
            @media #{$sm} {
                &-wrapper {
                    max-width: 75%;
                    margin: 0 auto;
                }
            }
            &:last-child {
                margin: 0 0;
            }
            &:hover {
                --tg-theme-primary: var(--tg-theme-secondary);
                & .tournament__list-live a {
                    color: var(--tg-common-color-white);
                    & i {
                        color: var(--tg-theme-secondary);
                    }
                }
            }
            &::before {
                content: "";
                position: absolute;
                left: 13%;
                top: 19px;
                width: 200px;
                height: 60px;
                background: var(--tg-theme-primary);
                @include transition(.3s);
                z-index: -1;
                @media #{$md} {
                    display: none;
                }
            }
            & svg {
                display: block;
                width: 100%;
                @media #{$lg} {
                    height: auto;
                }
                @media #{$md} {
                    display: none;
                }
            }
            & .background-path {
                fill: var(--tg-border-1);
                stroke: #4c4c4c;
                stroke-width: 0.25px;
                fill-rule: evenodd;
            }
        }
        &-content {
            position: absolute;
            left: 60px;
            right: 50px;
            top: 50%;
            @include transform(translateY(-50%));
            @include flexbox();
            align-items: center;
            @media #{$lg} {
                left: 50px;
                right: 45px;
            }
            @media #{$md} {
                position: relative;
                left: 0;
                right: 0;
                top: 0;
                @include transform(translateY(0));
                flex-wrap: wrap;
                gap: 40px 0;
            }
        }
        &-thumb {
            width: 150px;
            flex: 0 0 auto;
            @media #{$lg} {
                width: 115px;
            }
            @media #{$md} {
                width: 50%;
            }
            & img {
                max-width: 89px;
                max-height: 91px;
                @media #{$lg} {
                    max-width: 80px;
                    max-height: 75px;
                }
            }
        }
        &-name {
            padding-left: 35px;
            position: relative;
            z-index: 1;
            &::before {
                content: "";
                position: absolute;
                left: 1px;
                top: 50%;
                @include transform(translateY(-50%));
                height: 53px;
                width: 1px;
                background: var(--tg-common-color-white);
                background: radial-gradient(circle, var(--tg-common-color-white) 0%, transparent 100%);
                opacity: .18;
                z-index: 1;
            }
            &::after {
                content: "";
                position: absolute;
                left: 0;
                top: 50%;
                @include transform(translateY(-50%));
                height: 60px;
                width: 3px;
                background: #121920;
            }
        }
        &-name {
            width: 205px;
            flex: 0 0 auto;
            @media #{$md} {
                width: 50%;
                flex: 0 0 auto;
                padding-left: 20px;
                text-align: right;
            }
            & .team-name {
                font-size: 18px;
                margin: 0 0 8px;
            }
            & .status {
                display: block;
                text-transform: uppercase;
                font-size: 13px;
                font-weight: var(--tg-fw-bold);
                font-family: var(--tg-heading-font-family);
                letter-spacing: 1px;
                color: var(--tg-theme-primary);
                padding-left: 13px;
                position: relative;
                line-height: 1;
                @include transition(.3s);
                &::before {
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 3px;
                    width: 8px;
                    height: 8px;
                    background: currentColor;
                    @include border-radius(50%);
                }
                @media #{$md} {
                    padding-left: 0;
                    padding-right: 13px;
                    &::before {
                        left: auto;
                        right: 0;
                    }
                }
            }
        }
        &-prize {
            width: 216px;
            flex: 0 0 auto;
            padding-left: 55px;
            position: relative;
            @media #{$lg} {
                width: 180px;
                padding-left: 40px;
            }
            @media #{$md} {
                width: 50%;
                padding: 0 20px 0 0;
                &::before {
                    display: none;
                }
            }
            &::before {
                content: "";
                position: absolute;
                left: 1px;
                top: 50%;
                @include transform(translateY(-50%));
                height: 53px;
                width: 1px;
                background: var(--tg-common-color-white);
                background: radial-gradient(circle, var(--tg-common-color-white) 0%, transparent 100%);
                opacity: .18;
                z-index: 1;
            }
            & .title {
                font-size: 15px;
                color: var(--tg-body-color);
                letter-spacing: 1px;
                margin: 0 0 3px;
            }
            & i {
                color: var(--tg-theme-primary);
                font-size: 14px;
                margin-right: 7px;
                @include transition(.3s);
            }
            & span {
                color: var(--tg-theme-primary);
                font-family: var(--tg-heading-font-family);
                font-size: 17px;
                font-weight: var(--tg-fw-semi-bold);
                letter-spacing: 1px;
                @include transition(.3s);
            }
        }
        &-time {
            width: 216px;
            flex: 0 0 auto;
            padding-left: 55px;
            position: relative;
            @media #{$lg} {
                width: 180px;
                padding-left: 40px;
            }
            @media #{$md} {
                width: 50%;
                padding-left: 20px;
                text-align: right;
            }
            &::before {
                content: "";
                position: absolute;
                left: 1px;
                top: 50%;
                @include transform(translateY(-50%));
                height: 53px;
                width: 1px;
                background: var(--tg-common-color-white);
                background: radial-gradient(circle, var(--tg-common-color-white) 0%, transparent 100%);
                opacity: .18;
                z-index: 1;
            }
            & .title {
                font-size: 15px;
                color: var(--tg-body-color);
                letter-spacing: 1px;
                margin: 0 0 3px;
            }
            & i {
                font-size: 14px;
                margin-right: 7px;
            }
            & span {
                color: var(--tg-body-color);
                font-family: var(--tg-heading-font-family);
                font-size: 17px;
                font-weight: var(--tg-fw-semi-bold);
                text-transform: uppercase;
                letter-spacing: 1px;
            }
        }
        &-live {
            margin-left: auto;
            @media #{$md} {
                margin: 0 auto;
            }
            & a {
                display: inline-block;
                background: #0c1217;
                text-transform: uppercase;
                font-family: var(--tg-heading-font-family);
                font-weight: var(--tg-fw-bold);
                font-size: 13px;
                color: var(--tg-body-color);
                padding: 12px 45px;
                letter-spacing: 1px;
                @include border-radius(6px);
                @media #{$lg} {
                    padding: 12px 22px;
                }
                & i {
                    margin-left: 3px;
                    @include transition(.3s);
                }
            }
        }
    }
    &__details {
        &-area {
            padding: 120px 0;
        }
        &-content {
            background: #182029;
            padding: 40px 45px 40px 40px;
            -webkit-clip-path: polygon(100% 0, 100% calc(100% - 25px), calc(100% - 25px) 100%, 0 100%, 0 0);
            clip-path: polygon(100% 0, 100% calc(100% - 25px), calc(100% - 25px) 100%, 0 100%, 0 0);
            border: 1px solid #232a30;
            font-weight: var(--tg-fw-medium);
            @media #{$lg} {
                padding: 30px 25px;
            }
            @media #{$xs} {
                padding: 25px 20px;
            }
            @media #{$sm} {
                padding: 30px 25px;
            }
            & p {
                font-weight: var(--tg-fw-medium);
            }
            & .title {
                font-size: 32px;
                margin: 0 0 18px;
                @media #{$xs} {
                    font-size: 24px;
                    margin: 0 0 15px;
                }
            }
            & .blog-post-meta {
                padding-bottom: 30px;
                border-bottom: 1px solid #323c46;
                margin: 0 0 30px;
                @media #{$xs} {
                    margin: 0 0 25px;
                }
            }
            .blog-details-bottom {
                border-top: 1px solid #323c46;
            }
        }
        &-video {
            margin: 35px 0 30px;
            & .popup-video {
                position: absolute;
                top: 50%;
                left: 50%;
                @include transform(translate(-50%, -50%));
                font-size: 68px;
                color: var(--tg-common-color-white);
                z-index: 1;
                @media #{$xs} {
                    font-size: 54px;
                }
                &:hover {
                    color: var(--tg-theme-secondary);
                }
            }
        }
        &-form {
            margin: 40px 0 0;
            &-title {
                font-size: 24px;
                margin: 0 0 10px;
            }
            & form {
                margin: 25px 0 0;
            }
            & input {
                display: block;
                width: 100%;
                border: 1px solid #23292f;
                background: var(--tg-common-color-black);
                padding: 15px 30px;
                margin: 0 0 12px;
                @include border-radius(4px);
                &::placeholder {
                    opacity: .6;
                }
            }
            &-btn {
                display: inline-block;
                background: var(--tg-theme-primary);
                color: var(--tg-common-color-black);
                font-family: var(--tg-heading-font-family);
                font-weight: var(--tg-fw-bold);
                text-transform: uppercase;
                border: none;
                -webkit-clip-path: polygon(100% 0, 100% calc(100% - 20px), calc(100% - 20px) 100%, 0 100%, 0 0);
                clip-path: polygon(100% 0, 100% calc(100% - 20px), calc(100% - 20px) 100%, 0 100%, 0 0);
                padding: 14px 40px;
                font-size: 17px;
                letter-spacing: 1px;
                @include border-radius(4px);
                margin-top: 15px;
                @media #{$xs} {
                    -webkit-clip-path: polygon(100% 0, 100% calc(100% - 15px), calc(100% - 15px) 100%, 0 100%, 0 0);
                    clip-path: polygon(100% 0, 100% calc(100% - 15px), calc(100% - 15px) 100%, 0 100%, 0 0);
                    padding: 12px 32px;
                }
                &:hover {
                    background: var(--tg-theme-secondary);
                    color: var(--tg-common-color-black);
                }
            }
        }
    }
    &__sidebar {
        & .shop__widget,
        & .shop__widget-inner {
            border-color: #232a30;
        }
        & .shop__widget-inner {
            background: #182029;
        }
    }
    &__advertisement {
        & img {
            width: 100%;
            @include border-radius(5px);
        }
    }
}
.trending {
    &__matches {
        &-item {
            @include flexbox();
            align-items: center;
            margin: 0 0 20px;
            &:last-child {
                margin: 0 0;
            }
        }
        &-thumb {
            width: 73px;
            flex: 0 0 auto;
            margin-right: 18px;
            & img {
                @include border-radius(5px);
            }
        }
        &-content {
            @include flexbox();
            flex-grow: 1;
            justify-content: space-between;
            & .title {
                font-size: 18px;
                margin: 0 0 5px;
            }
            & .price {
                display: block;
                font-size: 14px;
                font-family: var(--tg-heading-font-family);
                font-weight: var(--tg-fw-semi-bold);
                color: var(--tg-theme-primary);
                letter-spacing: 1px;
            }
            & .play {
                & a {
                    display: block;
                    color: var(--tg-body-color);
                    &:hover {
                        color: var(--tg-theme-secondary);
                    }
                }
            }
        }
    }
}
input:focus-visible {
    outline: none !important;
    border-radius: none !important;
}
