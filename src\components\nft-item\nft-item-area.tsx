import nft_data from "../../data/nft-data";
import TextAnimation from "../common/text-animation";
import NftItemBox from "./nft-item-box";

const NftItemArea = () => {
  return (
    <section className="nft-item__area" style={{ marginTop: "-200px" }}>
      <div className="section__title text-center mb-60">
        <TextAnimation title="Services Provided" />
      </div>
      <div className="container custom-container">
        <div className="row justify-content-center">
          {nft_data.slice(0, 6).map((item) => (
            <div
              key={item.id}
              className="col-xxl-4 col-xl-5 col-lg-6 col-md-9 mbx"
            >
              <NftItemBox item={item} />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default NftItemArea;
