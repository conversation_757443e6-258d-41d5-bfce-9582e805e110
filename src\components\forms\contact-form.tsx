import { useForm, Submit<PERSON><PERSON><PERSON> } from "react-hook-form";
import ErrorMsg from "../common/err-message";
import { notifySuccess, notifyError } from "../../utils/toast";

interface IFormInput {
  name: string;
  phone: string;
  email: string;
  project: string;
  message: string;
}

const ContactForm = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<IFormInput>();
  const onSubmit: SubmitHandler<IFormInput> = async (data) => {
    try {
      const response = await fetch("https://formspree.io/f/xeqwrjjn", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        notifySuccess("Enquiry sent successfully!");
        reset();
      } else {
        notifyError("Failed to send message. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      notifyError("An error occurred. Please try again later.");
    }
  };
  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      id="contact-form"
      style={{ marginTop: -70 }}
    >
      <div className="row">
        <div className="col-sm-6">
          <ErrorMsg msg={errors.name?.message as string} />
          <div className="input-grp">
            <input
              {...register("name", { required: `Name is required!` })}
              name="name"
              id="name"
              type="text"
              placeholder="Name *"
            />
          </div>
        </div>
        <div className="col-sm-6">
          <ErrorMsg msg={errors.phone?.message as string} />
          <div className="input-grp">
            <input
              {...register("phone", { required: `Phone number is required!` })}
              name="phone"
              id="phone"
              type="text"
              placeholder="Phone number *"
            />
          </div>
        </div>
        <div className="col-sm-12">
          <ErrorMsg msg={errors.email?.message as string} />
          <div className="input-grp">
            <input
              {...register("email", { required: `Email is required!` })}
              name="email"
              id="email"
              type="email"
              placeholder="Email *"
            />
          </div>
        </div>
        <div className="col-sm-12">
          <ErrorMsg msg={errors.project?.message as string} />
          <div className="input-grp" style={{ height: 60 }}>
            <select
              {...register("project", { required: "Project is required!" })}
              name="project"
              id="project"
              className="form-control"
              style={{
                height: 60,
                backgroundColor: "#000000",
                color: "#FFFFFF",
                border: "solid #262f39",
                paddingLeft: 25,
                borderRadius: 0,
              }}
            >
              <option value="" disabled selected>
                Startup or Project type *
              </option>
              <option value="Website">Website</option>
              <option value="Wordpress">Wordpress</option>
              <option value="Shopify">Shopify</option>
              <option value="Web App">Web App</option>
              <option value="Mobile App">Mobile App</option>
              <option value="SaaS Software Implement">SaaS</option>
            </select>
          </div>
        </div>
      </div>
      <div className="input-grp message-grp">
        <ErrorMsg msg={errors.message?.message as string} />
        <textarea
          {...register("message", { required: `Message is required!` })}
          id="message"
          name="message"
          cols={30}
          rows={10}
          placeholder="What are you going to build ?"
        />
      </div>
      <button type="submit" className="submit-btn">
        Hire me ?
      </button>
    </form>
  );
};

export default ContactForm;
