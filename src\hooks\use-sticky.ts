import { useEffect, useState } from "react";

const useSticky = () => {
  const [sticky, setSticky] = useState(false);
  const [isStickyVisible, setIsStickyVisible] = useState(false);

  useEffect(() => {
    // const headerElement = document.getElementById("sticky-header");
    // const headerHeight = headerElement?.offsetHeight || 0;

    const handleScroll = () => {
      const windowTop = window.scrollY;

      // Check if the scrolling is triggered by user interaction
      const isUserScroll =
        document.scrollingElement === document.documentElement;

      // I changed here
      if (isUserScroll) {
        setSticky(true);
      } else {
        setSticky(false);
        setIsStickyVisible(false);
      }

      if (sticky && isUserScroll) {
        if (windowTop < lastScrollTop) {
          setIsStickyVisible(true);
        } else {
          setIsStickyVisible(false);
        }
      }

      lastScrollTop = windowTop;
    };

    let lastScrollTop = 0;
    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [sticky]);

  return {
    sticky,
    isStickyVisible,
  };
};

export default useSticky;
