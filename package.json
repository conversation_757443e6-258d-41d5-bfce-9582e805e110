{"name": "mykd-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@popperjs/core": "^2.11.8", "bootstrap": "^5.3.2", "react": "^18.2.0", "react-countup": "^6.5.0", "react-dom": "^18.2.0", "react-github-calendar": "^4.1.6", "react-helmet-async": "^2.0.3", "react-hook-form": "^7.48.2", "react-intersection-observer": "^9.5.3", "react-modal-video": "^2.0.1", "react-parallax-mouse": "^2.1.0", "react-photo-view": "^1.2.3", "react-range": "^1.8.14", "react-router-dom": "^6.20.1", "react-scroll": "^1.9.0", "react-slick": "^0.29.0", "react-timer-hook": "^3.0.7", "react-toastify": "^9.1.3", "recharts": "^2.15.1", "sass": "^1.69.5", "slick-carousel": "^1.8.1", "swiper": "^11.0.5", "vivus": "^0.4.6", "wow.js": "^1.2.2"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-modal-video": "^1.2.3", "@types/react-scroll": "^1.8.10", "@types/react-slick": "^0.23.12", "@types/vivus": "^0.4.7", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0"}}