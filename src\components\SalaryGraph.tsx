import {FC, useState, useMemo, useCallback } from "react";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  Area,
} from "recharts";

// Define interfaces for our data types
interface SalaryData {
  index: number;
  salary: number;
}

interface TooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    payload: SalaryData;
  }>;
}

const SalaryGraph: FC = () => {
  const [currency, setCurrency] = useState<"INR" | "USD">("INR");
  const [timeframe, setTimeframe] = useState<"month" | "year">("month");

  // Exchange rate (approximate)
  const exchangeRate = 0.012; // 1 INR ≈ 0.012 USD

  // Format numbers with commas for Indian format (fixed for 7-digit numbers)
  const formatIndianCurrency = useCallback((value: number): string => {
    const strValue = value.toString();

    // Handle Indian number system formatting (1,00,000 format)
    // For values like 19,50,000 instead of 1,9,50,000
    if (strValue.length <= 3) {
      return strValue;
    }

    let lastThree = strValue.substring(strValue.length - 3);
    let remaining = strValue.substring(0, strValue.length - 3);
    let result = "";

    // Add commas every two digits in the remaining part
    while (remaining.length > 0) {
      let chunk =
        remaining.length > 2
          ? remaining.substring(remaining.length - 2)
          : remaining;
      remaining = remaining.substring(0, remaining.length - chunk.length);

      result = chunk + (result ? "," + result : result);
    }

    return result + "," + lastThree;
  }, []);

  // Salary data without month labels
  const baseSalaryData: SalaryData[] = [
    { index: 1, salary: 10000 },
    { index: 2, salary: 5000 },
    { index: 3, salary: 15000 },
    { index: 4, salary: 30000 },
    { index: 5, salary: 50000 },
    { index: 6, salary: 80000 },
    { index: 7, salary: 62500 },
    { index: 8, salary: 90000 },
    { index: 9, salary: 152500 },
    { index: 10, salary: 62500 },
    { index: 11, salary: 100000 },
    { index: 12, salary: 162500 },
  ];

  // Calculate salary data based on timeframe with useMemo
  const salaryData = useMemo(
    () =>
      baseSalaryData.map((item) => ({
        ...item,
        salary: timeframe === "year" ? item.salary * 12 : item.salary,
      })),
    [timeframe]
  );

  // Format the salary based on selected currency with useMemo
  const formatSalary = useCallback(
    (value: number): string => {
      if (currency === "INR") {
        return `₹${formatIndianCurrency(value)}`;
      } else {
        return `$${(value * exchangeRate).toFixed(2)}`;
      }
    },
    [currency, formatIndianCurrency, exchangeRate]
  );

  // Custom tooltip to display formatted values with useCallback
  const CustomTooltip = useCallback(
    ({ active, payload }: TooltipProps) => {
      if (active && payload && payload.length) {
        return (
          <div
            className="salary-tooltip"
            style={{
              backgroundColor: "rgba(255, 255, 255, 0.9)",
              padding: "10px",
              border: "1px solid #10B981",
              borderRadius: "4px",
              boxShadow: "0 2px 6px rgba(0, 0, 0, 0.1)",
            }}
          >
            <p
              className="salary-value"
              style={{
                color: "#059669",
                fontWeight: "bold",
                margin: 0,
              }}
            >
              {formatSalary(payload[0].value)} per {timeframe}
            </p>
          </div>
        );
      }
      return null;
    },
    [formatSalary, timeframe]
  );

  return (
    <div
      className="section-title section-title-3 text-center mb-60 sag"
      style={{ paddingBottom: "100px" }}
    >
      <h2 className="title" style={{ color: "#ffffff" }}>
        Networth
      </h2>
      <div className="salary-container">
        <div
          className="salary-card"
          style={{
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.3)",
            borderRadius: "8px",
            padding: "20px",
            backgroundColor: "#000000",
          }}
        >
          <div
            className="toggle-container"
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              marginBottom: "20px",
              color: "#ffffff",
            }}
          >
            {/* Month/Year toggle */}
            <span
              className={
                timeframe === "month"
                  ? "timeframe-active"
                  : "timeframe-inactive"
              }
              style={{
                fontWeight: timeframe === "month" ? "bold" : "normal",
                color: timeframe === "month" ? "#10B981" : "#6b7280",
              }}
            >
              Month
            </span>
            <div
              className="toggle-switch"
              onClick={useCallback(
                () => setTimeframe(timeframe === "month" ? "year" : "month"),
                [timeframe, setTimeframe]
              )}
              style={{
                width: "48px",
                height: "24px",
                backgroundColor: "#e5e7eb",
                borderRadius: "12px",
                margin: "0 10px",
                position: "relative",
                cursor: "pointer",
              }}
            >
              <div
                style={{
                  width: "20px",
                  height: "20px",
                  backgroundColor: "#10B981",
                  borderRadius: "50%",
                  position: "absolute",
                  top: "2px",
                  left: timeframe === "year" ? "26px" : "2px",
                  transition: "left 0.3s ease",
                  zIndex: 1,
                }}
              ></div>
            </div>
            <span
              className={
                timeframe === "year" ? "timeframe-active" : "timeframe-inactive"
              }
              style={{
                fontWeight: timeframe === "year" ? "bold" : "normal",
                color: timeframe === "year" ? "#10B981" : "#6b7280",
              }}
            >
              Year
            </span>
            {/* Spacer between toggles */}
            <div style={{ width: "30px" }}></div>
            {/* Currency toggle */}
            <span
              className={
                currency === "INR" ? "currency-active" : "currency-inactive"
              }
              style={{
                fontWeight: currency === "INR" ? "bold" : "normal",
                color: currency === "INR" ? "#10B981" : "#6b7280",
              }}
            >
              INR
            </span>
            <div
              className="toggle-switch"
              onClick={useCallback(
                () => setCurrency(currency === "INR" ? "USD" : "INR"),
                [currency, setCurrency]
              )}
              style={{
                width: "48px",
                height: "24px",
                backgroundColor: "#e5e7eb",
                borderRadius: "12px",
                margin: "0 10px",
                position: "relative",
                cursor: "pointer",
              }}
            >
              <div
                style={{
                  width: "20px",
                  height: "20px",
                  backgroundColor: "#10B981",
                  borderRadius: "50%",
                  position: "absolute",
                  top: "2px",
                  left: currency === "USD" ? "26px" : "2px",
                  transition: "left 0.3s ease",
                  zIndex: 1,
                }}
              ></div>
            </div>
            <span
              className={
                currency === "USD" ? "currency-active" : "currency-inactive"
              }
              style={{
                fontWeight: currency === "USD" ? "bold" : "normal",
                color: currency === "USD" ? "#10B981" : "#6b7280",
              }}
            >
              USD
            </span>
          </div>

          <div
            className="chart-container"
            style={{ height: "400px", width: "100%" }}
          >
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={salaryData}
                margin={{ top: 5, right: 20, left: 20, bottom: 5 }}
              >
                <defs>
                  <linearGradient
                    id="greenGradient"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop offset="5%" stopColor="#10B981" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#10B981" stopOpacity={0.1} />
                  </linearGradient>
                </defs>
                {/* Grid lines removed */}
                <XAxis
                  dataKey="index"
                  stroke="#444444"
                  tickLine={false}
                  axisLine={true}
                  tick={false}
                />
                <YAxis
                  stroke="#888888"
                  tickFormatter={(value) => {
                    if (currency === "INR") {
                      return value >= 1000 ? `₹${value / 1000}k` : `₹${value}`;
                    } else {
                      return value * exchangeRate >= 1000
                        ? `$${((value * exchangeRate) / 1000).toFixed(1)}k`
                        : `$${(value * exchangeRate).toFixed(0)}`;
                    }
                  }}
                  tickLine={false}
                  axisLine={false}
                />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  type="monotone"
                  dataKey="salary"
                  stroke="none"
                  fillOpacity={1}
                  fill="url(#greenGradient)"
                />
                <Line
                  type="monotone"
                  dataKey="salary"
                  stroke="#10B981"
                  strokeWidth={3}
                  dot={{
                    fill: "#ffffff",
                    r: 5,
                    strokeWidth: 3,
                    stroke: "#10B981",
                  }}
                  activeDot={{
                    fill: "#059669",
                    r: 7,
                    strokeWidth: 2,
                    stroke: "#ffffff",
                  }}
                  connectNulls={true}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalaryGraph;
