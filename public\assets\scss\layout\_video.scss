@use '../utils' as *;

/*=============================
    08. Video
===============================*/
.video {
    &-bg {
        background-position: center;
        background-size: cover;
        min-height: 565px;
        @include flexbox();
        align-items: center;
        position: relative;
        padding: 100px 0;
        z-index: 1;
        background-attachment: fixed;
        &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: #1b242e;
            opacity: .22;
            z-index: -1;
        }
        &::after {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-image: url(/assets/img/bg/video_overlay.png);
            background-position: center;
            background-size: cover;
            opacity: .38;
            z-index: -1;
        }
    }
    &__content {
        & .popup-video {
            display: inline-block;
            font-size: 86px;
            line-height: 1;
            box-shadow: 0px 5px 12px 0px rgba(0, 0, 0, 0.13);
            color: var(--tg-common-color-white);
            margin: 0 0 14px;
            &:hover {
                color: var(--tg-theme-primary);
            }
        }
        & .title {
            font-size: 55px;
            font-weight: var(--tg-fw-extra-bold);
            margin: 0 0 6px;
            text-shadow: 0px 3px 7px rgba(0, 0, 0, 0.33);
            letter-spacing: 3px;
            @media #{$xs} {
                font-size: 48px;
                letter-spacing: 1px;
                line-height: 1.1;
            }
            & span {
                color: var(--tg-theme-primary);
            }
        }
        & p {
            margin: 0 0 30px;
            font-size: 16px;
            font-weight: var(--tg-fw-medium);
            color: #fefefe;
            letter-spacing: .9px;
        }
    }
}
.mfp-iframe-holder .mfp-content {
    max-width: 1200px;
    @media #{$xl} {
        max-width: 900px;
    }
}