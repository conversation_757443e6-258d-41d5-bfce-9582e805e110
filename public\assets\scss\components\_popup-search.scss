@use '../utils' as *;

/*=============================
	1. Search Popup
===============================*/
.search {
    &__popup-wrap {
        position: fixed;
        left: 0;
        top: 0;
        height: 100vh;
        width: 100%;
        z-index: 99;
        margin-top: -370px;
        transform: translateY(-100%);
        -webkit-transition: all 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        -moz-transition: all 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        -o-transition: all 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        transition: all 1500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        -webkit-transition-timing-function: cubic-bezier(0.860, 0.000, 0.070, 1.000);
        -moz-transition-timing-function: cubic-bezier(0.860, 0.000, 0.070, 1.000);
        -o-transition-timing-function: cubic-bezier(0.860, 0.000, 0.070, 1.000);
        transition-timing-function: cubic-bezier(0.860, 0.000, 0.070, 1.000);
        &::after {
            content: "";
            position: absolute;
            left: 0;
            top: 100%;
            width: 100%;
            height: 370px;
            background-image: url(/assets/img/bg/search_wave.png);
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            margin-top: 0px;
        }
        & input:focus-visible {
            outline: none;
            border-radius: none;
        }
    }
    &__layer {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100vh;
        width: 100%;
        background-color: rgba(15, 22, 27, 0.90);
        -webkit-transition: all 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        -moz-transition: all 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        -o-transition: all 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        transition: all 1500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        -webkit-transition-timing-function: cubic-bezier(0.860, 0.000, 0.070, 1.000);
        -moz-transition-timing-function: cubic-bezier(0.860, 0.000, 0.070, 1.000);
        -o-transition-timing-function: cubic-bezier(0.860, 0.000, 0.070, 1.000);
        transition-timing-function: cubic-bezier(0.860, 0.000, 0.070, 1.000);
        z-index: -1;
    }
    &__close {
        position: absolute;
        top: 5%;
        right: 5%;
        font-size: 30px;
        color: var(--tg-theme-primary);
        cursor: pointer;
    }
    &__wrap {
        position: absolute;
        left: 0;
        right: 0;
        top: 50%;
        @include transform(translateY(-50%));
        & .title {
            font-size: 47px;
            margin: 0 0 70px 0;
            font-family: var(--tg-heading-font-family);
            font-weight: var(--tg-fw-extra-bold);
            text-transform: uppercase;
            color: var(--tg-theme-primary);
            letter-spacing: -1px;
            & span {
                color: var(--tg-common-color-white);
                text-shadow: -2px 2.5px 0px rgb(69 248 130 / 66%);
                letter-spacing: 5px;
            }
        }
    }
    &__form {
        position: relative;
        & form {
            & input {
                display: block;
                width: 100%;
                border: none;
                padding: 10px 50px 20px;
                text-align: center;
                font-weight: 500;
                font-size: 30px;
                background: transparent;
                color: var(--tg-common-color-white);
                &::placeholder {
                    font-size: 30px;
                    opacity: .5;
                }
            }
            & button {
                position: absolute;
                right: 20px;
                background: transparent;
                border: 0;
                font-size: 25px;
                color: var(--tg-theme-primary);
                top: 50%;
                @include transform(translateY(-50%));
            }
        }
        &::after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            width: 0;
            height: 2px;
            background: var(--tg-theme-primary);
            @include transition-2(all 600ms ease);
        }
    }
    &__active {
        &.search__popup-wrap {
            @include transform(translateY(0%));
            margin-top: 0;
        }
        & .search__form {
            &::after {
                width: 100%;
                -webkit-transition-delay: 1200ms;
                -moz-transition-delay: 1200ms;
                -ms-transition-delay: 1200ms;
                -o-transition-delay: 1200ms;
                transition-delay: 1200ms;
            }
        }
    }
}

input:focus-visible {
    outline: 2px solid crimson;
    border-radius: 3px;
}