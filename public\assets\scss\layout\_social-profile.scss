@use '../utils' as *;

/*=============================
    13. Social Profile
===============================*/
.social {
    &-bg {
        background-position: bottom center;
        padding: 140px 0 150px;
        margin: -18px 0 -30px;
        filter: drop-shadow(0px 1px 0 rgba(255, 255, 255, 0.07));
        position: relative;
        background-size: cover;
        @media #{$lg} {
            padding: 120px 0 120px;
        }
        & .gutter-20 {
            --bs-gutter-x: 20px;
            gap: 20px 0;
        }
    }
    &__item {
        & a {
            @include flexbox();
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            background-color: #0a0b0f;
            border: 3px solid var(--tg-common-color-teal);
            width: 195px;
            height: 160px;
            position: relative;
            margin: 0 0 5px;
            @media #{$lg} {
                width: 100%;
                height: 115px;
            }
            & svg {
                fill: var(--tg-common-color-teal);
                fill-rule: evenodd;
                position: absolute;
                left: -3px;
                bottom: -8px;
            }
            & i {
                font-size: 44px;
                color: #d8d8d8;
                @include transition(.3s);
                @media #{$lg} {
                    font-size: 40px;
                }
            }
            & span {
                display: block;
                text-transform: uppercase;
                color: var(--tg-common-color-teal);
                font-size: 14px;
                font-weight: var(--tg-fw-medium);
                letter-spacing: 2px;
                line-height: 1;
                margin: 22px 0 0;
                @media #{$lg} {
                    margin: 15px 0 0;
                }
            }
            &:hover {
                & i {
                    color: var(--tg-common-color-teal);
                }
            }
        }
    }
}